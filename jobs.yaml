# DataEntryAI Configuration File
# This file defines jobs, schemas, and application settings

# Application Settings
app:
  name: "DataEntryAI"
  version: "1.0.0"
  debug: false
  log_level: "INFO"
  max_file_size_mb: 50
  supported_formats:
    - pdf
    - docx
    - txt
    - csv
    - png
    - jpg
    - jpeg

# AI Configuration
ai:
  provider: "groq"
  model: "llama3-8b-8192"
  api_key_env: "GROQ_API_KEY"
  temperature: 0.1
  max_tokens: 2000
  timeout: 30

# Web Crawler Configuration
crawler:
  max_pages: 10
  max_depth: 2
  delay_between_requests: 1.0
  timeout: 30
  max_concurrent: 5
  follow_external_links: false
  user_agent: "DataEntryAI-Crawler/1.0"
  respect_robots_txt: true

# OCR Configuration
ocr:
  tesseract_config: "--psm 6"
  languages: ["eng"]
  confidence_threshold: 60

# Export Configuration
export:
  default_format: "xlsx"
  include_metadata: true
  include_summary: true
  auto_format: true

# Job Definitions
jobs:
  # Contact Information Extraction Job
  contact_extraction:
    name: "Contact Information Extraction"
    description: "Extract contact details from documents and web pages"
    enabled: true
    schema: "contact_info"
    sources:
      - type: "file"
        formats: ["pdf", "docx", "txt"]
      - type: "web"
        max_pages: 5
    output:
      format: "xlsx"
      filename: "contacts_{timestamp}.xlsx"
      include_summary: true

  # Financial Data Processing Job
  financial_processing:
    name: "Financial Data Processing"
    description: "Extract and process financial information from documents"
    enabled: true
    schema: "financial_data"
    sources:
      - type: "file"
        formats: ["pdf", "csv", "xlsx"]
    output:
      format: "xlsx"
      filename: "financial_data_{timestamp}.xlsx"
      include_charts: true
    validation:
      required_fields: ["amount", "date"]
      min_confidence: 0.7

  # Invoice Processing Job
  invoice_processing:
    name: "Invoice Processing"
    description: "Extract structured data from invoices"
    enabled: true
    schema: "invoice_data"
    sources:
      - type: "file"
        formats: ["pdf", "png", "jpg"]
    ocr:
      enabled: true
      languages: ["eng"]
    output:
      format: "xlsx"
      filename: "invoices_{timestamp}.xlsx"
    validation:
      required_fields: ["invoice_number", "amount", "vendor"]
      min_confidence: 0.8

# Data Schemas
schemas:
  # Contact Information Schema
  contact_info:
    name: "contact_info"
    description: "Extract contact information from text"
    fields:
      - name: "name"
        type: "name"
        description: "Person's full name"
        required: true
        examples: ["John Smith", "Jane Doe", "Dr. Robert Johnson"]
      - name: "email"
        type: "email"
        description: "Email address"
        required: false
        pattern: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
        examples: ["<EMAIL>", "<EMAIL>"]
      - name: "phone"
        type: "phone"
        description: "Phone number"
        required: false
        examples: ["******-123-4567", "(*************", "************"]
      - name: "address"
        type: "address"
        description: "Physical address"
        required: false
        examples: ["123 Main St, City, State 12345"]
      - name: "company"
        type: "text"
        description: "Company or organization name"
        required: false
        examples: ["Acme Corp", "Tech Solutions Inc"]

  # Financial Data Schema
  financial_data:
    name: "financial_data"
    description: "Extract financial information from text"
    fields:
      - name: "amount"
        type: "currency"
        description: "Monetary amount"
        required: true
        examples: ["$1,234.56", "€500.00", "£99.99"]
      - name: "date"
        type: "date"
        description: "Transaction or report date"
        required: false
        examples: ["2024-01-15", "01/15/2024", "15-01-2024"]
      - name: "description"
        type: "text"
        description: "Transaction description"
        required: false
        examples: ["Payment for services", "Monthly subscription"]
      - name: "category"
        type: "text"
        description: "Expense or income category"
        required: false
        examples: ["Office Supplies", "Travel", "Software"]
      - name: "percentage"
        type: "percentage"
        description: "Percentage value (interest, tax, etc.)"
        required: false
        examples: ["5.5%", "12.5", "0.75%"]

  # Invoice Data Schema
  invoice_data:
    name: "invoice_data"
    description: "Extract invoice information from documents"
    fields:
      - name: "invoice_number"
        type: "text"
        description: "Invoice number or ID"
        required: true
        examples: ["INV-2024-001", "12345", "ABC-789"]
      - name: "vendor"
        type: "text"
        description: "Vendor or supplier name"
        required: true
        examples: ["ABC Company", "XYZ Services"]
      - name: "amount"
        type: "currency"
        description: "Total invoice amount"
        required: true
        examples: ["$1,500.00", "€750.50"]
      - name: "date"
        type: "date"
        description: "Invoice date"
        required: false
        examples: ["2024-01-15", "01/15/2024"]
      - name: "due_date"
        type: "date"
        description: "Payment due date"
        required: false
        examples: ["2024-02-15", "02/15/2024"]
      - name: "tax_amount"
        type: "currency"
        description: "Tax amount"
        required: false
        examples: ["$150.00", "€75.05"]
      - name: "customer_id"
        type: "text"
        description: "Customer ID or reference"
        required: false
        examples: ["CUST-001", "12345"]