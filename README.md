# DataEntryAI 🤖

A comprehensive data entry automation system with AI-powered extraction, web crawling, and intelligent data matching capabilities.

## Features

### 🔍 **Data Extraction**
- **Multi-format Support**: Extract data from PDFs, Word documents, images, text files, and CSV
- **OCR Integration**: Advanced OCR capabilities using Tesseract for image-based documents
- **Batch Processing**: Process multiple files simultaneously with async operations
- **Metadata Extraction**: Capture file metadata and processing statistics

### 🌐 **Web Crawling**
- **Intelligent Crawling**: Configurable depth and page limits with rate limiting
- **Content Extraction**: Clean text extraction from web pages with link discovery
- **Respect Robots.txt**: Built-in robots.txt compliance and ethical crawling practices
- **Async Operations**: High-performance concurrent crawling capabilities

### 🎯 **AI-Powered Data Matching**
- **Schema-Based Extraction**: Define custom data schemas for structured extraction
- **Groq AI Integration**: Leverage advanced language models for intelligent data parsing
- **Confidence Scoring**: Get confidence scores for extracted data fields
- **Field Validation**: Automatic data type validation and pattern matching

### 📊 **Excel Export & Analytics**
- **Rich Excel Export**: Multi-sheet workbooks with formatting and charts
- **Data Validation**: Built-in data validation and conditional formatting
- **Summary Analytics**: Automatic generation of summary statistics and insights
- **Multiple Formats**: Export to Excel, CSV, and JSON formats

### ⚙️ **Configuration Management**
- **YAML Configuration**: Flexible job and schema configuration system
- **Environment Variables**: Secure API key and settings management
- **Job Templates**: Pre-defined job templates for common use cases
- **Schema Library**: Built-in schemas for contacts, financial data, and invoices

### 🖥️ **Web Interface**
- **Streamlit UI**: Modern, responsive web interface
- **Real-time Progress**: Live progress tracking and status updates
- **Interactive Results**: Expandable result views with detailed information
- **Error Handling**: Comprehensive error reporting with recovery suggestions

## Quick Start

### Prerequisites

- Python 3.8 or higher
- Tesseract OCR (for image processing)
- Groq API key (for AI features)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/DataEntryAI.git
   cd DataEntryAI
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Install Tesseract OCR**
   
   **Ubuntu/Debian:**
   ```bash
   sudo apt-get install tesseract-ocr
   ```
   
   **macOS:**
   ```bash
   brew install tesseract
   ```
   
   **Windows:**
   Download from: https://github.com/UB-Mannheim/tesseract/wiki

5. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env and add your Groq API key
   ```

6. **Run the application**
   ```bash
   streamlit run app.py
   ```

### Getting Your Groq API Key

1. Visit [Groq Console](https://console.groq.com/)
2. Sign up or log in to your account
3. Navigate to API Keys section
4. Create a new API key
5. Add it to your `.env` file:
   ```
   GROQ_API_KEY=your_api_key_here
   ```

## Usage

### File Processing

1. **Upload Files**: Use the file uploader to select documents (PDF, Word, images, text)
2. **Configure Options**: Choose processing job, enable OCR, set output format
3. **Process**: Click "Process Files" to start extraction
4. **Review Results**: View extracted content and metadata in expandable sections

### Web Crawling

1. **Enter URLs**: Input one or more URLs to crawl (one per line)
2. **Set Parameters**: Configure max pages, crawl depth, and request delay
3. **Start Crawling**: Begin the crawling process with real-time progress
4. **Analyze Results**: Review extracted content, titles, and discovered links

### Data Matching

1. **Select Schema**: Choose from available data schemas (contacts, financial, etc.)
2. **Set Confidence**: Adjust minimum confidence threshold for matches
3. **Run Matching**: Process extracted content against the selected schema
4. **Export Results**: Download structured data in Excel format

### Configuration

The application uses YAML configuration files for jobs and schemas:

```yaml
# jobs.yaml
jobs:
  contact_extraction:
    name: "Contact Information Extraction"
    description: "Extract contact details from documents"
    schema: "contact_info"
    sources:
      - type: "file"
        formats: ["pdf", "docx", "txt"]
    output:
      format: "xlsx"
      filename: "contacts_{timestamp}.xlsx"

schemas:
  contact_info:
    name: "contact_info"
    description: "Extract contact information"
    fields:
      - name: "name"
        type: "name"
        description: "Person's full name"
        required: true
      - name: "email"
        type: "email"
        description: "Email address"
        required: false
```

## Architecture

### Core Modules

- **`core/extractor.py`**: Multi-format data extraction with OCR support
- **`core/crawler.py`**: Asynchronous web crawling with rate limiting
- **`core/matcher.py`**: AI-powered data matching and schema validation
- **`core/excel_writer.py`**: Advanced Excel export with formatting
- **`core/config.py`**: Configuration management and validation
- **`core/error_handler.py`**: Comprehensive error handling and logging

### Data Flow

```
Input Sources → Extraction → AI Matching → Validation → Export
     ↓              ↓           ↓            ↓         ↓
  Files/URLs → Raw Content → Structured → Validated → Excel/CSV
```

## API Reference

### DataExtractor

```python
from core import DataExtractor

extractor = DataExtractor()
result = await extractor.extract_from_file("document.pdf")
```

### WebCrawler

```python
from core import WebCrawler, CrawlConfig

config = CrawlConfig(max_pages=10, max_depth=2)
async with WebCrawler(config) as crawler:
    results = await crawler.crawl_website("https://example.com")
```

### DataMatcher

```python
from core import DataMatcher

matcher = DataMatcher(api_key="your_api_key")
result = await matcher.match_data(text, schema_name="contact_info")
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/dataentryai)
- 📖 Documentation: [Full documentation](https://docs.dataentryai.com)
- 🐛 Issues: [GitHub Issues](https://github.com/your-username/DataEntryAI/issues)

## Acknowledgments

- [Streamlit](https://streamlit.io/) for the amazing web framework
- [Groq](https://groq.com/) for powerful AI capabilities
- [Tesseract](https://github.com/tesseract-ocr/tesseract) for OCR functionality
- [OpenPyXL](https://openpyxl.readthedocs.io/) for Excel manipulation
