# DataEntryAI Environment Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# AI Configuration
# =============================================================================

# Groq API Key (Required for AI features)
# Get your API key from: https://console.groq.com/
GROQ_API_KEY=your_groq_api_key_here

# =============================================================================
# Application Configuration
# =============================================================================

# Application environment (development, staging, production)
APP_ENV=development

# Debug mode (true/false)
DEBUG=true

# Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Maximum file size for uploads (in MB)
MAX_FILE_SIZE_MB=50

# =============================================================================
# Database Configuration (if using database storage)
# =============================================================================

# Database URL (optional - for storing results)
# DATABASE_URL=sqlite:///dataentry.db

# =============================================================================
# Web Crawler Configuration
# =============================================================================

# Default user agent for web crawling
CRAWLER_USER_AGENT=DataEntryAI-Crawler/1.0

# Default delay between requests (seconds)
CRAWLER_DELAY=1.0

# Maximum concurrent requests
CRAWLER_MAX_CONCURRENT=5

# =============================================================================
# OCR Configuration
# =============================================================================

# Tesseract executable path (if not in PATH)
# TESSERACT_CMD=/usr/bin/tesseract

# Default OCR language
OCR_LANGUAGE=eng

# OCR confidence threshold (0-100)
OCR_CONFIDENCE_THRESHOLD=60

# =============================================================================
# Export Configuration
# =============================================================================

# Default export format
DEFAULT_EXPORT_FORMAT=xlsx

# Include metadata in exports
INCLUDE_METADATA=true

# Include summary sheets in exports
INCLUDE_SUMMARY=true

# =============================================================================
# Security Configuration
# =============================================================================

# Secret key for session management (generate a random string)
SECRET_KEY=your_secret_key_here

# Allowed file extensions (comma-separated)
ALLOWED_EXTENSIONS=pdf,docx,txt,csv,png,jpg,jpeg,tiff,bmp

# Maximum number of files per upload
MAX_FILES_PER_UPLOAD=10

# =============================================================================
# Performance Configuration
# =============================================================================

# Number of worker processes for parallel processing
WORKER_PROCESSES=4

# Timeout for AI requests (seconds)
AI_TIMEOUT=30

# Timeout for web requests (seconds)
WEB_TIMEOUT=30

# Cache TTL (seconds)
CACHE_TTL=3600

# =============================================================================
# Monitoring and Analytics
# =============================================================================

# Enable analytics tracking
ENABLE_ANALYTICS=false

# Analytics service URL (if using external service)
# ANALYTICS_URL=https://your-analytics-service.com

# Error reporting service URL (if using external service)
# ERROR_REPORTING_URL=https://your-error-service.com

# =============================================================================
# Email Configuration (for notifications)
# =============================================================================

# SMTP server configuration (optional)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your_app_password
# SMTP_USE_TLS=true

# Default sender email
# DEFAULT_FROM_EMAIL=<EMAIL>

# =============================================================================
# Cloud Storage Configuration (optional)
# =============================================================================

# AWS S3 Configuration
# AWS_ACCESS_KEY_ID=your_aws_access_key
# AWS_SECRET_ACCESS_KEY=your_aws_secret_key
# AWS_S3_BUCKET=your-bucket-name
# AWS_REGION=us-east-1

# Google Cloud Storage Configuration
# GOOGLE_CLOUD_PROJECT=your-project-id
# GOOGLE_CLOUD_BUCKET=your-bucket-name
# GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account.json

# Azure Blob Storage Configuration
# AZURE_STORAGE_CONNECTION_STRING=your_connection_string
# AZURE_CONTAINER_NAME=your-container-name

# =============================================================================
# Development Configuration
# =============================================================================

# Enable development features
DEV_MODE=true

# Show debug information in UI
SHOW_DEBUG_INFO=false

# Enable hot reloading
HOT_RELOAD=true

# Development server port
DEV_PORT=8501

# =============================================================================
# Production Configuration
# =============================================================================

# Production server configuration
# SERVER_HOST=0.0.0.0
# SERVER_PORT=8080

# SSL configuration
# SSL_CERT_PATH=/path/to/cert.pem
# SSL_KEY_PATH=/path/to/key.pem

# Load balancer configuration
# BEHIND_PROXY=true
# PROXY_HEADERS=true

# =============================================================================
# Backup Configuration
# =============================================================================

# Enable automatic backups
ENABLE_BACKUPS=false

# Backup directory
BACKUP_DIR=./backups

# Backup retention days
BACKUP_RETENTION_DAYS=30

# =============================================================================
# Rate Limiting
# =============================================================================

# Enable rate limiting
ENABLE_RATE_LIMITING=true

# Requests per minute per IP
RATE_LIMIT_PER_MINUTE=60

# Rate limit storage backend (memory, redis)
RATE_LIMIT_STORAGE=memory

# Redis configuration (if using Redis for rate limiting)
# REDIS_URL=redis://localhost:6379/0

# =============================================================================
# Feature Flags
# =============================================================================

# Enable experimental features
ENABLE_EXPERIMENTAL_FEATURES=false

# Enable beta features
ENABLE_BETA_FEATURES=false

# Enable advanced analytics
ENABLE_ADVANCED_ANALYTICS=false

# Enable batch processing
ENABLE_BATCH_PROCESSING=true

# Enable real-time processing
ENABLE_REALTIME_PROCESSING=false

# =============================================================================
# Integration Configuration
# =============================================================================

# Webhook URLs for notifications
# WEBHOOK_SUCCESS_URL=https://your-webhook.com/success
# WEBHOOK_ERROR_URL=https://your-webhook.com/error

# Slack integration
# SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# Microsoft Teams integration
# TEAMS_WEBHOOK_URL=https://your-teams-webhook.com

# =============================================================================
# Custom Configuration
# =============================================================================

# Add your custom environment variables here
# CUSTOM_SETTING_1=value1
# CUSTOM_SETTING_2=value2
