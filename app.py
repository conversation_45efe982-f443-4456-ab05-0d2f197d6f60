"""
DataEntryAI - Streamlit Web Application

A comprehensive data entry automation system with AI-powered extraction,
web crawling, and intelligent data matching capabilities.
"""

import asyncio
import logging
from datetime import datetime
from typing import List, Dict, Any

import streamlit as st
import pandas as pd
from dotenv import load_dotenv

# Import core modules
from core import (
    ConfigManager, DataExtractor, WebCrawler, DataMatcher, ExcelWriter,
    ExtractionResult, CrawlResult, MatchResult, DataSchema, FieldSchema, DataType
)

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Page configuration
st.set_page_config(
    page_title="DataEntryAI",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state
if 'config_manager' not in st.session_state:
    st.session_state.config_manager = ConfigManager()

if 'extraction_results' not in st.session_state:
    st.session_state.extraction_results = []

if 'crawl_results' not in st.session_state:
    st.session_state.crawl_results = []

if 'match_results' not in st.session_state:
    st.session_state.match_results = []


def main():
    """Main application function"""
    st.title("🤖 DataEntryAI")
    st.markdown("*Intelligent Data Extraction and Processing Platform*")

    # Sidebar navigation
    st.sidebar.title("Navigation")
    page = st.sidebar.selectbox(
        "Choose a page",
        [
            "🏠 Home",
            "📄 File Processing",
            "🌐 Web Crawling",
            "🎯 Data Matching",
            "📊 Results & Export",
            "⚙️ Configuration",
            "📈 Analytics"
        ]
    )

    # Display current configuration status
    config_status = check_configuration()
    if not config_status["valid"]:
        st.sidebar.error("⚠️ Configuration Issues")
        for issue in config_status["issues"]:
            st.sidebar.warning(f"• {issue}")
    else:
        st.sidebar.success("✅ Configuration Valid")

    # Route to appropriate page
    if page == "🏠 Home":
        show_home_page()
    elif page == "📄 File Processing":
        show_file_processing_page()
    elif page == "🌐 Web Crawling":
        show_web_crawling_page()
    elif page == "🎯 Data Matching":
        show_data_matching_page()
    elif page == "📊 Results & Export":
        show_results_page()
    elif page == "⚙️ Configuration":
        show_configuration_page()
    elif page == "📈 Analytics":
        show_analytics_page()


def check_configuration() -> Dict[str, Any]:
    """Check configuration validity"""
    config_manager = st.session_state.config_manager
    issues = config_manager.validate_config()

    return {
        "valid": len(issues) == 0,
        "issues": issues
    }


def show_home_page():
    """Display home page"""
    st.header("Welcome to DataEntryAI")

    col1, col2, col3 = st.columns(3)

    with col1:
        st.subheader("📄 File Processing")
        st.write("Extract data from PDFs, Word documents, images, and text files using OCR and AI.")
        if st.button("Start File Processing", key="home_file"):
            st.session_state.page = "📄 File Processing"
            st.rerun()

    with col2:
        st.subheader("🌐 Web Crawling")
        st.write("Crawl websites and extract structured data from web pages automatically.")
        if st.button("Start Web Crawling", key="home_web"):
            st.session_state.page = "🌐 Web Crawling"
            st.rerun()

    with col3:
        st.subheader("🎯 Data Matching")
        st.write("Use AI to match and structure extracted data according to predefined schemas.")
        if st.button("Start Data Matching", key="home_match"):
            st.session_state.page = "🎯 Data Matching"
            st.rerun()

    # Quick stats
    st.subheader("📊 Quick Statistics")
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("Files Processed", len(st.session_state.extraction_results))

    with col2:
        st.metric("Pages Crawled", len(st.session_state.crawl_results))

    with col3:
        st.metric("Data Matches", len(st.session_state.match_results))

    with col4:
        config_manager = st.session_state.config_manager
        st.metric("Available Jobs", len(config_manager.list_jobs()))

    # Recent activity
    st.subheader("📋 Recent Activity")
    if st.session_state.extraction_results or st.session_state.crawl_results:
        activity_data = []

        for result in st.session_state.extraction_results[-5:]:
            activity_data.append({
                "Type": "File Extraction",
                "Status": "✅ Success" if result.success else "❌ Failed",
                "Details": f"File type: {result.file_type}"
            })

        for result in st.session_state.crawl_results[-5:]:
            activity_data.append({
                "Type": "Web Crawling",
                "Status": "✅ Success" if result.success else "❌ Failed",
                "Details": f"URL: {result.url[:50]}..."
            })

        if activity_data:
            df = pd.DataFrame(activity_data)
            st.dataframe(df, use_container_width=True)
    else:
        st.info("No recent activity. Start by processing files or crawling websites!")


def show_file_processing_page():
    """Display file processing page"""
    st.header("📄 File Processing")

    # File upload
    uploaded_files = st.file_uploader(
        "Upload files for processing",
        type=['pdf', 'docx', 'txt', 'csv', 'png', 'jpg', 'jpeg'],
        accept_multiple_files=True,
        help="Supported formats: PDF, Word, Text, CSV, Images"
    )

    # Processing options
    col1, col2 = st.columns(2)

    with col1:
        st.subheader("Processing Options")

        # Job selection
        config_manager = st.session_state.config_manager
        available_jobs = config_manager.list_jobs()

        if available_jobs:
            selected_job = st.selectbox(
                "Select processing job",
                ["Custom"] + available_jobs,
                help="Choose a predefined job or create custom processing"
            )
        else:
            selected_job = "Custom"
            st.info("No predefined jobs available. Using custom processing.")

        # OCR options
        enable_ocr = st.checkbox("Enable OCR for images", value=True)
        if enable_ocr:
            ocr_languages = st.multiselect(
                "OCR Languages",
                ["eng", "spa", "fra", "deu", "ita"],
                default=["eng"]
            )

    with col2:
        st.subheader("Output Options")

        output_format = st.selectbox(
            "Output format",
            ["Excel (.xlsx)", "CSV (.csv)", "JSON (.json)"],
            index=0
        )

        include_metadata = st.checkbox("Include metadata", value=True)
        include_raw_text = st.checkbox("Include raw extracted text", value=False)

    # Process files
    if uploaded_files and st.button("🚀 Process Files", type="primary"):
        process_uploaded_files(
            uploaded_files,
            selected_job,
            enable_ocr,
            output_format,
            include_metadata,
            include_raw_text
        )

    # Display results
    if st.session_state.extraction_results:
        st.subheader("📋 Processing Results")

        # Summary
        total_files = len(st.session_state.extraction_results)
        successful = sum(1 for r in st.session_state.extraction_results if r.success)

        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Total Files", total_files)
        with col2:
            st.metric("Successful", successful)
        with col3:
            st.metric("Success Rate", f"{(successful/total_files)*100:.1f}%")

        # Detailed results
        for i, result in enumerate(st.session_state.extraction_results):
            with st.expander(f"File {i+1}: {result.file_type.upper()} - {'✅' if result.success else '❌'}"):
                if result.success:
                    st.success("Extraction successful")
                    st.text_area("Extracted Content", result.content[:500] + "..." if len(result.content) > 500 else result.content)

                    if result.metadata:
                        st.json(result.metadata)
                else:
                    st.error(f"Extraction failed: {result.error_message}")


async def process_uploaded_files(
    uploaded_files,
    selected_job: str,
    enable_ocr: bool,
    output_format: str,
    include_metadata: bool,
    include_raw_text: bool
):
    """Process uploaded files"""
    progress_bar = st.progress(0)
    status_text = st.empty()

    extractor = DataExtractor()
    results = []

    for i, uploaded_file in enumerate(uploaded_files):
        status_text.text(f"Processing {uploaded_file.name}...")

        try:
            # Read file bytes
            file_bytes = uploaded_file.read()

            # Extract data
            result = await extractor.extract_from_bytes(file_bytes, uploaded_file.name)
            results.append(result)

        except Exception as e:
            logger.error(f"Error processing {uploaded_file.name}: {e}")
            results.append(ExtractionResult(
                content="",
                metadata={},
                file_type="unknown",
                success=False,
                error_message=str(e)
            ))

        # Update progress
        progress_bar.progress((i + 1) / len(uploaded_files))

    # Store results
    st.session_state.extraction_results.extend(results)

    status_text.text("✅ Processing complete!")
    st.success(f"Processed {len(uploaded_files)} files successfully!")


def run_async(coro):
    """Run async function in Streamlit"""
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

    return loop.run_until_complete(coro)


def show_web_crawling_page():
    """Display web crawling page"""
    st.header("🌐 Web Crawling")

    # URL input
    col1, col2 = st.columns([2, 1])

    with col1:
        urls_input = st.text_area(
            "Enter URLs to crawl (one per line)",
            height=100,
            placeholder="https://example.com\nhttps://another-site.com"
        )

    with col2:
        st.subheader("Crawling Options")
        max_pages = st.number_input("Max pages per site", min_value=1, max_value=100, value=10)
        max_depth = st.number_input("Max crawl depth", min_value=1, max_value=5, value=2)
        delay = st.number_input("Delay between requests (seconds)", min_value=0.1, max_value=10.0, value=1.0)

    # Process URLs
    if urls_input and st.button("🚀 Start Crawling", type="primary"):
        urls = [url.strip() for url in urls_input.split('\n') if url.strip()]
        if urls:
            run_async(crawl_websites(urls, max_pages, max_depth, delay))

    # Display results
    if st.session_state.crawl_results:
        st.subheader("📋 Crawling Results")

        # Summary
        total_pages = len(st.session_state.crawl_results)
        successful = sum(1 for r in st.session_state.crawl_results if r.success)

        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Total Pages", total_pages)
        with col2:
            st.metric("Successful", successful)
        with col3:
            st.metric("Success Rate", f"{(successful/total_pages)*100:.1f}%")

        # Detailed results
        for i, result in enumerate(st.session_state.crawl_results):
            with st.expander(f"Page {i+1}: {result.url} - {'✅' if result.success else '❌'}"):
                if result.success:
                    st.success("Crawling successful")
                    if result.title:
                        st.write(f"**Title:** {result.title}")
                    st.text_area("Content", result.content[:500] + "..." if len(result.content) > 500 else result.content)
                    st.write(f"**Links found:** {len(result.links)}")
                else:
                    st.error(f"Crawling failed: {result.error_message}")


async def crawl_websites(urls: List[str], max_pages: int, max_depth: int, delay: float):
    """Crawl websites"""
    progress_bar = st.progress(0)
    status_text = st.empty()

    from core.crawler import CrawlConfig

    config = CrawlConfig(
        max_pages=max_pages,
        max_depth=max_depth,
        delay_between_requests=delay
    )

    async with WebCrawler(config) as crawler:
        results = []

        for i, url in enumerate(urls):
            status_text.text(f"Crawling {url}...")

            try:
                site_results = await crawler.crawl_website(url, max_pages)
                results.extend(site_results)
            except Exception as e:
                logger.error(f"Error crawling {url}: {e}")

            progress_bar.progress((i + 1) / len(urls))

    st.session_state.crawl_results.extend(results)
    status_text.text("✅ Crawling complete!")
    st.success(f"Crawled {len(results)} pages successfully!")


def show_data_matching_page():
    """Display data matching page"""
    st.header("🎯 Data Matching")

    # Check if we have data to match
    all_content = []

    # Collect content from extraction results
    for result in st.session_state.extraction_results:
        if result.success and result.content:
            all_content.append(result.content)

    # Collect content from crawl results
    for result in st.session_state.crawl_results:
        if result.success and result.content:
            all_content.append(result.content)

    if not all_content:
        st.warning("No extracted content available. Please process files or crawl websites first.")
        return

    # Schema selection
    config_manager = st.session_state.config_manager
    available_schemas = config_manager.list_schemas()

    if not available_schemas:
        st.error("No schemas available. Please configure schemas first.")
        return

    col1, col2 = st.columns(2)

    with col1:
        selected_schema = st.selectbox(
            "Select data schema",
            available_schemas,
            help="Choose the schema to match data against"
        )

        confidence_threshold = st.slider(
            "Minimum confidence threshold",
            min_value=0.0,
            max_value=1.0,
            value=0.5,
            step=0.1
        )

    with col2:
        st.subheader("Schema Details")
        if selected_schema:
            schema_config = config_manager.get_schema_config(selected_schema)
            if schema_config:
                st.write(f"**Description:** {schema_config.description}")
                st.write("**Fields:**")
                for field in schema_config.fields:
                    required_text = " (Required)" if field.required else ""
                    st.write(f"• {field.name} ({field.type}){required_text}: {field.description}")

    # Process matching
    if st.button("🎯 Start Matching", type="primary"):
        api_key = config_manager.get_api_key()
        if not api_key:
            st.error("API key not configured. Please check your environment variables.")
            return

        run_async(match_data_with_schema(all_content, selected_schema, confidence_threshold, api_key))

    # Display results
    if st.session_state.match_results:
        st.subheader("📋 Matching Results")

        # Summary
        total_matches = len(st.session_state.match_results)
        successful = sum(1 for r in st.session_state.match_results if r.success)

        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Total Processed", total_matches)
        with col2:
            st.metric("Successful Matches", successful)
        with col3:
            avg_confidence = sum(r.confidence_score for r in st.session_state.match_results if r.success) / max(successful, 1)
            st.metric("Avg Confidence", f"{avg_confidence:.2f}")

        # Detailed results
        for i, result in enumerate(st.session_state.match_results):
            with st.expander(f"Match {i+1}: {result.schema_name} - {'✅' if result.success else '❌'}"):
                if result.success:
                    st.success(f"Matching successful (Confidence: {result.confidence_score:.2f})")

                    # Display matched data
                    st.subheader("Extracted Data")
                    for field_name, value in result.matched_data.items():
                        confidence = result.field_matches.get(field_name, 0)
                        st.write(f"**{field_name}:** {value} (Confidence: {confidence:.2f})")
                else:
                    st.error(f"Matching failed: {result.error_message}")


async def match_data_with_schema(content_list: List[str], schema_name: str, threshold: float, api_key: str):
    """Match data with selected schema"""
    progress_bar = st.progress(0)
    status_text = st.empty()

    matcher = DataMatcher(api_key=api_key)

    # Create schemas from config
    config_manager = st.session_state.config_manager
    schema_config = config_manager.get_schema_config(schema_name)

    if schema_config:
        # Convert config to DataSchema
        fields = []
        for field_config in schema_config.fields:
            field = FieldSchema(
                name=field_config.name,
                data_type=DataType(field_config.type),
                description=field_config.description,
                required=field_config.required,
                pattern=field_config.pattern,
                examples=field_config.examples
            )
            fields.append(field)

        schema = DataSchema(
            name=schema_config.name,
            description=schema_config.description,
            fields=fields
        )

        matcher.add_schema(schema)

    results = []

    for i, content in enumerate(content_list):
        status_text.text(f"Matching content {i+1}/{len(content_list)}...")

        try:
            result = await matcher.match_data(content, schema_name)
            if result.confidence_score >= threshold:
                results.append(result)
        except Exception as e:
            logger.error(f"Error matching content {i+1}: {e}")

        progress_bar.progress((i + 1) / len(content_list))

    st.session_state.match_results.extend(results)
    status_text.text("✅ Matching complete!")
    st.success(f"Found {len(results)} matches above threshold!")


def show_results_page():
    """Display results and export page"""
    st.header("📊 Results & Export")

    # Check if we have any results
    has_extraction = bool(st.session_state.extraction_results)
    has_crawling = bool(st.session_state.crawl_results)
    has_matching = bool(st.session_state.match_results)

    if not (has_extraction or has_crawling or has_matching):
        st.info("No results available yet. Process some files or crawl websites first!")
        return

    # Export options
    st.subheader("📤 Export Options")

    col1, col2, col3 = st.columns(3)

    with col1:
        export_extraction = st.checkbox("Include File Extraction Results", value=has_extraction, disabled=not has_extraction)

    with col2:
        export_crawling = st.checkbox("Include Web Crawling Results", value=has_crawling, disabled=not has_crawling)

    with col3:
        export_matching = st.checkbox("Include Data Matching Results", value=has_matching, disabled=not has_matching)

    # Export format
    export_format = st.selectbox(
        "Export format",
        ["Excel (.xlsx)", "CSV (.csv)", "JSON (.json)"],
        index=0
    )

    # Export button
    if st.button("📥 Export Results", type="primary"):
        export_results(export_extraction, export_crawling, export_matching, export_format)

    # Display summary statistics
    st.subheader("📈 Summary Statistics")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("Files Processed", len(st.session_state.extraction_results))
        if st.session_state.extraction_results:
            success_rate = sum(1 for r in st.session_state.extraction_results if r.success) / len(st.session_state.extraction_results)
            st.metric("File Success Rate", f"{success_rate*100:.1f}%")

    with col2:
        st.metric("Pages Crawled", len(st.session_state.crawl_results))
        if st.session_state.crawl_results:
            success_rate = sum(1 for r in st.session_state.crawl_results if r.success) / len(st.session_state.crawl_results)
            st.metric("Crawl Success Rate", f"{success_rate*100:.1f}%")

    with col3:
        st.metric("Data Matches", len(st.session_state.match_results))
        if st.session_state.match_results:
            avg_confidence = sum(r.confidence_score for r in st.session_state.match_results if r.success) / max(len(st.session_state.match_results), 1)
            st.metric("Avg Confidence", f"{avg_confidence:.2f}")

    with col4:
        total_items = len(st.session_state.extraction_results) + len(st.session_state.crawl_results)
        st.metric("Total Items", total_items)


def export_results(include_extraction: bool, include_crawling: bool, include_matching: bool, format_type: str):
    """Export results to file"""
    try:
        writer = ExcelWriter()
        writer.create_workbook()

        # Export extraction results
        if include_extraction and st.session_state.extraction_results:
            extraction_data = []
            for result in st.session_state.extraction_results:
                extraction_data.append({
                    "File Type": result.file_type,
                    "Success": result.success,
                    "Content Length": len(result.content) if result.content else 0,
                    "Error": result.error_message or "",
                    "Metadata": str(result.metadata)
                })

            if extraction_data:
                from core.excel_writer import SheetConfig, ColumnConfig
                sheet_config = SheetConfig(
                    name="File Extraction",
                    columns=[
                        ColumnConfig("File Type"),
                        ColumnConfig("Success"),
                        ColumnConfig("Content Length"),
                        ColumnConfig("Error"),
                        ColumnConfig("Metadata")
                    ],
                    data=extraction_data
                )
                writer.add_sheet(sheet_config)

        # Export crawling results
        if include_crawling and st.session_state.crawl_results:
            crawling_data = []
            for result in st.session_state.crawl_results:
                crawling_data.append({
                    "URL": result.url,
                    "Title": result.title or "",
                    "Success": result.success,
                    "Content Length": len(result.content) if result.content else 0,
                    "Links Count": len(result.links),
                    "Status Code": result.status_code or "",
                    "Error": result.error_message or ""
                })

            if crawling_data:
                from core.excel_writer import SheetConfig, ColumnConfig
                sheet_config = SheetConfig(
                    name="Web Crawling",
                    columns=[
                        ColumnConfig("URL"),
                        ColumnConfig("Title"),
                        ColumnConfig("Success"),
                        ColumnConfig("Content Length"),
                        ColumnConfig("Links Count"),
                        ColumnConfig("Status Code"),
                        ColumnConfig("Error")
                    ],
                    data=crawling_data
                )
                writer.add_sheet(sheet_config)

        # Export matching results
        if include_matching and st.session_state.match_results:
            matching_data = []
            for result in st.session_state.match_results:
                row = {
                    "Schema": result.schema_name,
                    "Success": result.success,
                    "Confidence": result.confidence_score,
                    "Error": result.error_message or ""
                }

                # Add matched fields
                for field_name, value in result.matched_data.items():
                    row[f"Field_{field_name}"] = value

                matching_data.append(row)

            if matching_data:
                # Get all possible columns
                all_columns = set()
                for row in matching_data:
                    all_columns.update(row.keys())

                columns = [ColumnConfig(col) for col in sorted(all_columns)]

                from core.excel_writer import SheetConfig
                sheet_config = SheetConfig(
                    name="Data Matching",
                    columns=columns,
                    data=matching_data
                )
                writer.add_sheet(sheet_config)

        # Save file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"dataentry_results_{timestamp}.xlsx"
        writer.save(filename)

        # Provide download
        with open(filename, "rb") as file:
            st.download_button(
                label="📥 Download Results",
                data=file.read(),
                file_name=filename,
                mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            )

        st.success(f"Results exported successfully! File: {filename}")

    except Exception as e:
        st.error(f"Error exporting results: {e}")


def show_configuration_page():
    """Display configuration page"""
    st.header("⚙️ Configuration")

    config_manager = st.session_state.config_manager

    # API Configuration
    st.subheader("🔑 API Configuration")

    current_api_key = config_manager.get_api_key()
    api_key_status = "✅ Configured" if current_api_key else "❌ Not configured"
    st.write(f"**Groq API Key Status:** {api_key_status}")

    if not current_api_key:
        st.warning("Please set the GROQ_API_KEY environment variable to use AI features.")
        st.code("export GROQ_API_KEY=your_api_key_here")

    # Job Configuration
    st.subheader("🔧 Available Jobs")

    jobs = config_manager.list_jobs()
    if jobs:
        for job_name in jobs:
            job_config = config_manager.get_job_config(job_name)
            if job_config:
                with st.expander(f"Job: {job_name}"):
                    st.write(f"**Description:** {job_config.description}")
                    st.write(f"**Enabled:** {job_config.enabled}")
                    st.write(f"**Schema:** {job_config.schema}")
                    if job_config.sources:
                        st.write("**Sources:**")
                        for source in job_config.sources:
                            st.write(f"  • Type: {source.get('type', 'Unknown')}")
    else:
        st.info("No jobs configured.")

    # Schema Configuration
    st.subheader("📋 Available Schemas")

    schemas = config_manager.list_schemas()
    if schemas:
        for schema_name in schemas:
            schema_config = config_manager.get_schema_config(schema_name)
            if schema_config:
                with st.expander(f"Schema: {schema_name}"):
                    st.write(f"**Description:** {schema_config.description}")
                    st.write("**Fields:**")
                    for field in schema_config.fields:
                        required_text = " (Required)" if field.required else ""
                        st.write(f"  • {field.name} ({field.type}){required_text}: {field.description}")
    else:
        st.info("No schemas configured.")

    # Configuration validation
    st.subheader("✅ Configuration Validation")

    validation_results = config_manager.validate_config()
    if validation_results:
        st.error("Configuration issues found:")
        for issue in validation_results:
            st.write(f"• {issue}")
    else:
        st.success("Configuration is valid!")


def show_analytics_page():
    """Display analytics page"""
    st.header("📈 Analytics")

    # Check if we have data
    if not (st.session_state.extraction_results or st.session_state.crawl_results or st.session_state.match_results):
        st.info("No data available for analytics. Process some files or crawl websites first!")
        return

    # File type distribution
    if st.session_state.extraction_results:
        st.subheader("📄 File Type Distribution")

        file_types = {}
        for result in st.session_state.extraction_results:
            file_type = result.file_type
            file_types[file_type] = file_types.get(file_type, 0) + 1

        if file_types:
            df = pd.DataFrame(list(file_types.items()), columns=['File Type', 'Count'])
            st.bar_chart(df.set_index('File Type'))

    # Success rates
    st.subheader("📊 Success Rates")

    col1, col2, col3 = st.columns(3)

    with col1:
        if st.session_state.extraction_results:
            extraction_success = sum(1 for r in st.session_state.extraction_results if r.success)
            extraction_total = len(st.session_state.extraction_results)
            extraction_rate = extraction_success / extraction_total if extraction_total > 0 else 0

            st.metric("File Extraction", f"{extraction_rate*100:.1f}%")
            st.progress(extraction_rate)

    with col2:
        if st.session_state.crawl_results:
            crawl_success = sum(1 for r in st.session_state.crawl_results if r.success)
            crawl_total = len(st.session_state.crawl_results)
            crawl_rate = crawl_success / crawl_total if crawl_total > 0 else 0

            st.metric("Web Crawling", f"{crawl_rate*100:.1f}%")
            st.progress(crawl_rate)

    with col3:
        if st.session_state.match_results:
            match_success = sum(1 for r in st.session_state.match_results if r.success)
            match_total = len(st.session_state.match_results)
            match_rate = match_success / match_total if match_total > 0 else 0

            st.metric("Data Matching", f"{match_rate*100:.1f}%")
            st.progress(match_rate)

    # Confidence distribution
    if st.session_state.match_results:
        st.subheader("🎯 Confidence Score Distribution")

        confidence_scores = [r.confidence_score for r in st.session_state.match_results if r.success]
        if confidence_scores:
            df = pd.DataFrame(confidence_scores, columns=['Confidence'])
            st.histogram(df['Confidence'], bins=20)


if __name__ == "__main__":
    main()