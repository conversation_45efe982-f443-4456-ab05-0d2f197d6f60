#!/usr/bin/env python3
"""
Test Runner for DataEntryAI

Simple test runner that can be used without pytest if needed.
"""

import asyncio
import sys
import traceback
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))


class SimpleTestRunner:
    """Simple test runner for basic validation"""
    
    def __init__(self):
        self.passed = 0
        self.failed = 0
        self.errors = []
    
    def run_test(self, test_func, test_name):
        """Run a single test function"""
        try:
            print(f"Running {test_name}...", end=" ")
            
            if asyncio.iscoroutinefunction(test_func):
                asyncio.run(test_func())
            else:
                test_func()
            
            print("✅ PASSED")
            self.passed += 1
            
        except Exception as e:
            print("❌ FAILED")
            error_msg = f"{test_name}: {str(e)}"
            self.errors.append(error_msg)
            self.failed += 1
            print(f"  Error: {str(e)}")
    
    def run_all_tests(self):
        """Run all validation tests"""
        print("🚀 Starting DataEntryAI Production Validation Tests")
        print("=" * 60)
        
        # Test 1: Import validation
        self.run_test(self.test_imports, "Import Validation")
        
        # Test 2: Configuration validation
        self.run_test(self.test_configuration, "Configuration Validation")
        
        # Test 3: File structure validation
        self.run_test(self.test_file_structure, "File Structure Validation")
        
        # Test 4: Basic functionality
        self.run_test(self.test_basic_functionality, "Basic Functionality")
        
        # Test 5: Environment setup
        self.run_test(self.test_environment_setup, "Environment Setup")
        
        # Print summary
        print("\n" + "=" * 60)
        print(f"📊 Test Summary:")
        print(f"   ✅ Passed: {self.passed}")
        print(f"   ❌ Failed: {self.failed}")
        print(f"   📈 Success Rate: {(self.passed/(self.passed+self.failed)*100):.1f}%")
        
        if self.errors:
            print(f"\n❌ Errors:")
            for error in self.errors:
                print(f"   • {error}")
        
        if self.failed == 0:
            print("\n🎉 All tests passed! System is production-ready.")
            return True
        else:
            print(f"\n⚠️  {self.failed} test(s) failed. Please fix issues before production deployment.")
            return False
    
    def test_imports(self):
        """Test that all core modules can be imported"""
        try:
            from core import (
                ConfigManager, DataExtractor, WebCrawler, 
                DataMatcher, ExcelWriter
            )
            from core.error_handler import ErrorHandler
            
            # Test basic instantiation
            config = ConfigManager()
            extractor = DataExtractor()
            writer = ExcelWriter()
            handler = ErrorHandler()
            
            assert config is not None
            assert extractor is not None
            assert writer is not None
            assert handler is not None
            
        except ImportError as e:
            raise AssertionError(f"Failed to import core modules: {e}")
    
    def test_configuration(self):
        """Test configuration system"""
        from core import ConfigManager
        
        config_manager = ConfigManager()
        
        # Test basic configuration
        assert config_manager.app_config is not None
        assert config_manager.ai_config is not None
        
        # Test schemas are loaded
        schemas = config_manager.list_schemas()
        assert len(schemas) > 0, "No schemas loaded"
        
        # Test jobs are loaded
        jobs = config_manager.list_jobs()
        assert len(jobs) > 0, "No jobs loaded"
        
        # Test validation
        issues = config_manager.validate_config()
        # Only API key issue is acceptable for testing
        api_key_issues = [issue for issue in issues if "API key" in issue]
        other_issues = [issue for issue in issues if "API key" not in issue]
        
        if other_issues:
            raise AssertionError(f"Configuration issues: {other_issues}")
    
    def test_file_structure(self):
        """Test that required files exist"""
        required_files = [
            "app.py",
            "requirements.txt", 
            "jobs.yaml",
            "README.md",
            ".env.example",
            "core/__init__.py",
            "core/config.py",
            "core/extractor.py",
            "core/crawler.py",
            "core/matcher.py",
            "core/excel_writer.py",
            "core/error_handler.py"
        ]
        
        missing_files = []
        for file_path in required_files:
            if not Path(file_path).exists():
                missing_files.append(file_path)
        
        if missing_files:
            raise AssertionError(f"Missing required files: {missing_files}")
    
    def test_basic_functionality(self):
        """Test basic functionality without external dependencies"""
        from core import DataExtractor, ExcelWriter
        
        # Test extractor
        extractor = DataExtractor()
        assert extractor.get_file_type("test.pdf") == "pdf"
        assert extractor.get_file_type("test.txt") == "text"
        
        # Test Excel writer
        writer = ExcelWriter()
        workbook = writer.create_workbook()
        assert workbook is not None
        
        # Test data export (without saving)
        test_data = [{"name": "Test", "value": 123}]
        # This should not raise an exception
        writer.create_workbook()
    
    def test_environment_setup(self):
        """Test environment setup"""
        # Check .env.example exists and has required variables
        env_example = Path(".env.example")
        assert env_example.exists(), ".env.example file missing"
        
        with open(env_example, 'r') as f:
            env_content = f.read()
        
        required_vars = ["GROQ_API_KEY", "APP_ENV", "LOG_LEVEL"]
        missing_vars = []
        
        for var in required_vars:
            if var not in env_content:
                missing_vars.append(var)
        
        if missing_vars:
            raise AssertionError(f"Missing environment variables in .env.example: {missing_vars}")


def main():
    """Main function"""
    print("DataEntryAI Production Validation")
    print("This script validates that the system is ready for production deployment.")
    print()
    
    runner = SimpleTestRunner()
    success = runner.run_all_tests()
    
    if success:
        print("\n🚀 System is ready for production!")
        print("\nNext steps:")
        print("1. Set up your environment variables (copy .env.example to .env)")
        print("2. Add your Groq API key to the .env file")
        print("3. Install Tesseract OCR for image processing")
        print("4. Run: streamlit run app.py")
        sys.exit(0)
    else:
        print("\n🛑 System is NOT ready for production!")
        print("Please fix the issues above before deploying.")
        sys.exit(1)


if __name__ == "__main__":
    main()
