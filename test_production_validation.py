"""
Production Validation Tests

Comprehensive test suite to validate all functionality and ensure
the system is production-ready.
"""

import asyncio
import os
import tempfile
import pytest
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock

# Import core modules
from core import (
    ConfigManager, DataExtractor, WebCrawler, DataMatcher, ExcelWriter,
    ExtractionResult, CrawlResult, MatchResult, DataSchema, FieldSchema, DataType
)
from core.error_handler import <PERSON>rror<PERSON><PERSON><PERSON>, ErrorCategory, ErrorSeverity


class TestConfigurationValidation:
    """Test configuration system validation"""
    
    def test_config_manager_initialization(self):
        """Test configuration manager initializes correctly"""
        config_manager = ConfigManager()
        assert config_manager is not None
        assert config_manager.app_config is not None
        assert config_manager.ai_config is not None
    
    def test_default_schemas_available(self):
        """Test that default schemas are available"""
        config_manager = ConfigManager()
        schemas = config_manager.list_schemas()
        
        # Should have at least the schemas defined in jobs.yaml
        expected_schemas = ["contact_info", "financial_data", "invoice_data"]
        for schema in expected_schemas:
            assert schema in schemas
    
    def test_job_configuration_valid(self):
        """Test that job configurations are valid"""
        config_manager = ConfigManager()
        jobs = config_manager.list_jobs()
        
        for job_name in jobs:
            job_config = config_manager.get_job_config(job_name)
            assert job_config is not None
            assert job_config.name
            assert job_config.description
            
            # If job has a schema, it should exist
            if job_config.schema:
                assert job_config.schema in config_manager.list_schemas()
    
    def test_configuration_validation(self):
        """Test configuration validation"""
        config_manager = ConfigManager()
        issues = config_manager.validate_config()
        
        # Should only have API key issue if not set
        api_key = os.getenv("GROQ_API_KEY")
        if not api_key:
            assert len(issues) >= 1
            assert any("API key" in issue for issue in issues)
        else:
            assert len(issues) == 0


class TestDataExtraction:
    """Test data extraction functionality"""
    
    @pytest.fixture
    def extractor(self):
        """Create data extractor instance"""
        return DataExtractor()
    
    def test_extractor_initialization(self, extractor):
        """Test extractor initializes correctly"""
        assert extractor is not None
        assert hasattr(extractor, 'SUPPORTED_FORMATS')
    
    def test_file_type_detection(self, extractor):
        """Test file type detection"""
        assert extractor.get_file_type("test.pdf") == "pdf"
        assert extractor.get_file_type("test.docx") == "docx"
        assert extractor.get_file_type("test.png") == "image"
        assert extractor.get_file_type("test.txt") == "text"
        assert extractor.get_file_type("test.unknown") == "unknown"
    
    @pytest.mark.asyncio
    async def test_text_file_extraction(self, extractor):
        """Test text file extraction"""
        # Create temporary text file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            test_content = "This is a test document with sample content."
            f.write(test_content)
            temp_path = f.name
        
        try:
            result = await extractor.extract_from_file(temp_path)
            assert result.success
            assert test_content in result.content
            assert result.file_type == "text"
        finally:
            os.unlink(temp_path)
    
    @pytest.mark.asyncio
    async def test_nonexistent_file_handling(self, extractor):
        """Test handling of non-existent files"""
        result = await extractor.extract_from_file("nonexistent_file.pdf")
        assert not result.success
        assert "not found" in result.error_message.lower()
    
    @pytest.mark.asyncio
    async def test_batch_extraction(self, extractor):
        """Test batch file extraction"""
        # Create multiple temporary files
        temp_files = []
        for i in range(3):
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
                f.write(f"Test content {i}")
                temp_files.append(f.name)
        
        try:
            results = await extractor.batch_extract(temp_files)
            assert len(results) == 3
            assert all(result.success for result in results)
        finally:
            for temp_file in temp_files:
                os.unlink(temp_file)


class TestWebCrawling:
    """Test web crawling functionality"""
    
    @pytest.fixture
    def crawler_config(self):
        """Create crawler configuration"""
        from core.crawler import CrawlConfig
        return CrawlConfig(
            max_pages=2,
            max_depth=1,
            delay_between_requests=0.1,
            timeout=10
        )
    
    def test_crawler_initialization(self, crawler_config):
        """Test crawler initializes correctly"""
        crawler = WebCrawler(crawler_config)
        assert crawler is not None
        assert crawler.config == crawler_config
    
    def test_url_validation(self, crawler_config):
        """Test URL validation"""
        crawler = WebCrawler(crawler_config)
        
        # Valid URLs
        assert crawler._is_valid_url("https://example.com")
        assert crawler._is_valid_url("http://test.org")
        
        # Invalid URLs
        assert not crawler._is_valid_url("ftp://example.com")
        assert not crawler._is_valid_url("invalid-url")
    
    @pytest.mark.asyncio
    async def test_single_page_crawl_mock(self, crawler_config):
        """Test single page crawling with mock"""
        crawler = WebCrawler(crawler_config)
        
        # Mock the HTTP response
        mock_response = Mock()
        mock_response.status = 200
        mock_response.text = AsyncMock(return_value="""
            <html>
                <head><title>Test Page</title></head>
                <body>
                    <h1>Test Content</h1>
                    <p>This is test content.</p>
                    <a href="/link1">Link 1</a>
                </body>
            </html>
        """)
        mock_response.headers = {"content-type": "text/html"}
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            mock_get.return_value.__aenter__.return_value = mock_response
            
            await crawler.start_session()
            result = await crawler.crawl_single_page("https://example.com")
            await crawler.close_session()
            
            assert result.success
            assert result.title == "Test Page"
            assert "Test Content" in result.content


class TestDataMatching:
    """Test AI-powered data matching"""
    
    @pytest.fixture
    def mock_api_key(self):
        """Mock API key for testing"""
        return "test_api_key"
    
    def test_matcher_initialization(self, mock_api_key):
        """Test matcher initializes correctly"""
        matcher = DataMatcher(api_key=mock_api_key)
        assert matcher is not None
        assert matcher.api_key == mock_api_key
    
    def test_schema_management(self, mock_api_key):
        """Test schema management"""
        matcher = DataMatcher(api_key=mock_api_key)
        
        # Create test schema
        fields = [
            FieldSchema(
                name="name",
                data_type=DataType.NAME,
                description="Person's name",
                required=True
            ),
            FieldSchema(
                name="email",
                data_type=DataType.EMAIL,
                description="Email address",
                required=False
            )
        ]
        
        schema = DataSchema(
            name="test_schema",
            description="Test schema",
            fields=fields
        )
        
        matcher.add_schema(schema)
        assert "test_schema" in matcher.list_schemas()
        
        retrieved_schema = matcher.get_schema("test_schema")
        assert retrieved_schema is not None
        assert retrieved_schema.name == "test_schema"
    
    def test_data_type_validation(self, mock_api_key):
        """Test data type validation"""
        matcher = DataMatcher(api_key=mock_api_key)
        
        # Test email validation
        assert matcher._validate_data_type("<EMAIL>", DataType.EMAIL)
        assert not matcher._validate_data_type("invalid-email", DataType.EMAIL)
        
        # Test number validation
        assert matcher._validate_data_type("123.45", DataType.NUMBER)
        assert matcher._validate_data_type("1,234", DataType.NUMBER)
        assert not matcher._validate_data_type("not-a-number", DataType.NUMBER)
        
        # Test URL validation
        assert matcher._validate_data_type("https://example.com", DataType.URL)
        assert not matcher._validate_data_type("not-a-url", DataType.URL)


class TestExcelExport:
    """Test Excel export functionality"""
    
    @pytest.fixture
    def excel_writer(self):
        """Create Excel writer instance"""
        return ExcelWriter()
    
    def test_writer_initialization(self, excel_writer):
        """Test writer initializes correctly"""
        assert excel_writer is not None
        assert excel_writer.default_styles is not None
    
    def test_workbook_creation(self, excel_writer):
        """Test workbook creation"""
        workbook = excel_writer.create_workbook()
        assert workbook is not None
        assert excel_writer.workbook is not None
    
    def test_data_export(self, excel_writer):
        """Test data export functionality"""
        # Sample data
        data = [
            {"name": "John Doe", "email": "<EMAIL>", "age": 30},
            {"name": "Jane Smith", "email": "<EMAIL>", "age": 25}
        ]
        
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as f:
            temp_path = f.name
        
        try:
            excel_writer.export_data(data, temp_path, include_summary=False)
            
            # Verify file was created
            assert Path(temp_path).exists()
            assert Path(temp_path).stat().st_size > 0
        finally:
            if Path(temp_path).exists():
                os.unlink(temp_path)


class TestErrorHandling:
    """Test error handling system"""
    
    @pytest.fixture
    def error_handler(self):
        """Create error handler instance"""
        return ErrorHandler()
    
    def test_error_handler_initialization(self, error_handler):
        """Test error handler initializes correctly"""
        assert error_handler is not None
        assert error_handler.errors == []
    
    def test_error_handling(self, error_handler):
        """Test error handling functionality"""
        test_error = ValueError("Test error message")
        
        error_info = error_handler.handle_error(
            test_error,
            ErrorCategory.FILE_PROCESSING,
            ErrorSeverity.MEDIUM
        )
        
        assert error_info is not None
        assert error_info.category == ErrorCategory.FILE_PROCESSING
        assert error_info.severity == ErrorSeverity.MEDIUM
        assert "Test error message" in error_info.message
        assert len(error_handler.errors) == 1
    
    def test_error_summary(self, error_handler):
        """Test error summary generation"""
        # Add some test errors
        for i in range(3):
            error = ValueError(f"Test error {i}")
            error_handler.handle_error(
                error,
                ErrorCategory.FILE_PROCESSING,
                ErrorSeverity.MEDIUM
            )
        
        summary = error_handler.get_error_summary()
        assert summary["total"] == 3
        assert summary["by_category"]["file_processing"] == 3
        assert summary["by_severity"]["medium"] == 3


class TestIntegration:
    """Integration tests for the complete system"""
    
    @pytest.mark.asyncio
    async def test_end_to_end_file_processing(self):
        """Test complete file processing workflow"""
        # Create test file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            test_content = "John Doe\nemail: <EMAIL>\nphone: ******-123-4567"
            f.write(test_content)
            temp_path = f.name
        
        try:
            # Extract data
            extractor = DataExtractor()
            extraction_result = await extractor.extract_from_file(temp_path)
            
            assert extraction_result.success
            assert "John Doe" in extraction_result.content
            
            # Export to Excel
            excel_writer = ExcelWriter()
            data = [{
                "content": extraction_result.content,
                "file_type": extraction_result.file_type,
                "success": extraction_result.success
            }]
            
            with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as f:
                excel_path = f.name
            
            try:
                excel_writer.export_data(data, excel_path, include_summary=False)
                assert Path(excel_path).exists()
            finally:
                if Path(excel_path).exists():
                    os.unlink(excel_path)
                    
        finally:
            os.unlink(temp_path)
    
    def test_configuration_integration(self):
        """Test configuration system integration"""
        config_manager = ConfigManager()
        
        # Test that all required components are configured
        assert config_manager.app_config is not None
        assert config_manager.ai_config is not None
        assert config_manager.crawler_config is not None
        assert config_manager.ocr_config is not None
        assert config_manager.export_config is not None
        
        # Test that schemas and jobs are loaded
        assert len(config_manager.list_schemas()) > 0
        assert len(config_manager.list_jobs()) > 0


class TestProductionReadiness:
    """Test production readiness requirements"""
    
    def test_required_files_exist(self):
        """Test that all required files exist"""
        required_files = [
            "app.py",
            "requirements.txt",
            "jobs.yaml",
            "README.md",
            ".env.example"
        ]
        
        for file_path in required_files:
            assert Path(file_path).exists(), f"Required file {file_path} is missing"
    
    def test_core_modules_importable(self):
        """Test that all core modules can be imported"""
        try:
            from core import (
                ConfigManager, DataExtractor, WebCrawler, 
                DataMatcher, ExcelWriter
            )
            assert True
        except ImportError as e:
            pytest.fail(f"Failed to import core modules: {e}")
    
    def test_dependencies_available(self):
        """Test that all required dependencies are available"""
        required_packages = [
            "streamlit", "groq", "openpyxl", "python-docx",
            "PyPDF2", "pdfplumber", "Pillow", "pytesseract",
            "aiohttp", "aiofiles", "beautifulsoup4", "pyyaml",
            "python-dotenv"
        ]
        
        for package in required_packages:
            try:
                __import__(package.replace("-", "_"))
            except ImportError:
                pytest.fail(f"Required package {package} is not available")
    
    def test_environment_configuration(self):
        """Test environment configuration"""
        # Check that .env.example has all required variables
        env_example_path = Path(".env.example")
        assert env_example_path.exists()
        
        with open(env_example_path, 'r') as f:
            env_content = f.read()
        
        required_vars = [
            "GROQ_API_KEY",
            "APP_ENV",
            "LOG_LEVEL",
            "MAX_FILE_SIZE_MB"
        ]
        
        for var in required_vars:
            assert var in env_content, f"Required environment variable {var} not in .env.example"


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v", "--tb=short"])
