"""
Data Matcher Module

This module provides intelligent data matching and structuring capabilities
using AI (Groq API) to identify, categorize, and structure extracted data
according to predefined schemas.
"""

import asyncio
import json
import logging
import re
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from enum import Enum

from groq import AsyncGroq
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DataType(Enum):
    """Supported data types for matching"""
    TEXT = "text"
    NUMBER = "number"
    DATE = "date"
    EMAIL = "email"
    PHONE = "phone"
    URL = "url"
    ADDRESS = "address"
    NAME = "name"
    CURRENCY = "currency"
    PERCENTAGE = "percentage"


@dataclass
class FieldSchema:
    """Schema definition for a data field"""
    name: str
    data_type: DataType
    description: str
    required: bool = False
    pattern: Optional[str] = None
    examples: List[str] = field(default_factory=list)
    validation_rules: Dict[str, Any] = field(default_factory=dict)


@dataclass
class DataSchema:
    """Schema definition for structured data"""
    name: str
    description: str
    fields: List[FieldSchema]
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class MatchResult:
    """Result of data matching operation"""
    matched_data: Dict[str, Any]
    confidence_score: float
    schema_name: str
    field_matches: Dict[str, float]
    raw_extractions: Dict[str, List[str]]
    success: bool
    error_message: Optional[str] = None


class DataMatcher:
    """
    Intelligent data matching system using AI for data extraction and structuring
    """

    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the data matcher

        Args:
            api_key: Groq API key (if not provided, will use environment variable)
        """
        self.api_key = api_key or os.getenv("GROQ_API_KEY")
        if not self.api_key:
            raise ValueError(
                "Groq API key is required. Set GROQ_API_KEY environment variable "
                "or pass api_key parameter."
            )

        self.client = AsyncGroq(api_key=self.api_key)
        self.schemas: Dict[str, DataSchema] = {}
        self.default_model = "llama3-8b-8192"

    def add_schema(self, schema: DataSchema) -> None:
        """
        Add a data schema for matching

        Args:
            schema: DataSchema to add
        """
        self.schemas[schema.name] = schema
        logger.info(f"Added schema: {schema.name}")

    def remove_schema(self, schema_name: str) -> None:
        """
        Remove a data schema

        Args:
            schema_name: Name of schema to remove
        """
        if schema_name in self.schemas:
            del self.schemas[schema_name]
            logger.info(f"Removed schema: {schema_name}")

    def get_schema(self, schema_name: str) -> Optional[DataSchema]:
        """
        Get a data schema by name

        Args:
            schema_name: Name of schema to retrieve

        Returns:
            DataSchema if found, None otherwise
        """
        return self.schemas.get(schema_name)

    def list_schemas(self) -> List[str]:
        """
        List all available schema names

        Returns:
            List of schema names
        """
        return list(self.schemas.keys())

    async def _extract_with_ai(
        self, text: str, schema: DataSchema
    ) -> Dict[str, Any]:
        """
        Use AI to extract structured data from text

        Args:
            text: Text to extract data from
            schema: Schema to match against

        Returns:
            Extracted structured data
        """
        # Build prompt for AI extraction
        schema_description = self._build_schema_prompt(schema)

        prompt = f"""
Extract structured data from the following text according to the schema provided.

SCHEMA:
{schema_description}

TEXT TO ANALYZE:
{text[:4000]}  # Limit text length for API

INSTRUCTIONS:
1. Extract data that matches the schema fields
2. Return ONLY a valid JSON object with the extracted data
3. Use null for missing required fields
4. Include confidence scores (0-1) for each field
5. Format: {{"field_name": {{"value": "extracted_value", "confidence": 0.95}}}}

JSON OUTPUT:
"""

        try:
            response = await self.client.chat.completions.create(
                model=self.default_model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are a data extraction expert. Extract structured data from text and return only valid JSON."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.1,
                max_tokens=2000
            )

            # Parse AI response
            ai_response = response.choices[0].message.content.strip()

            # Extract JSON from response
            json_match = re.search(r'\{.*\}', ai_response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                return json.loads(json_str)
            else:
                logger.warning("No JSON found in AI response")
                return {}

        except Exception as e:
            logger.error(f"Error in AI extraction: {e}")
            return {}

    def _build_schema_prompt(self, schema: DataSchema) -> str:
        """
        Build a prompt description of the schema for AI

        Args:
            schema: DataSchema to describe

        Returns:
            String description of the schema
        """
        prompt_parts = [
            f"Schema Name: {schema.name}",
            f"Description: {schema.description}",
            "Fields:"
        ]

        for field in schema.fields:
            field_desc = f"  - {field.name} ({field.data_type.value})"
            if field.required:
                field_desc += " [REQUIRED]"
            field_desc += f": {field.description}"

            if field.examples:
                field_desc += f" Examples: {', '.join(field.examples[:3])}"

            prompt_parts.append(field_desc)

        return "\n".join(prompt_parts)

    def _validate_extracted_data(
        self, data: Dict[str, Any], schema: DataSchema
    ) -> Dict[str, float]:
        """
        Validate extracted data against schema and calculate confidence scores

        Args:
            data: Extracted data to validate
            schema: Schema to validate against

        Returns:
            Dictionary of field confidence scores
        """
        field_scores = {}

        for field in schema.fields:
            field_name = field.name

            if field_name in data:
                field_data = data[field_name]

                # Extract value and confidence if structured
                if isinstance(field_data, dict) and 'value' in field_data:
                    value = field_data['value']
                    confidence = field_data.get('confidence', 0.5)
                else:
                    value = field_data
                    confidence = 0.7  # Default confidence

                # Validate data type and pattern
                type_valid = self._validate_data_type(value, field.data_type)
                pattern_valid = self._validate_pattern(value, field.pattern)

                # Calculate final confidence
                validation_score = 1.0 if (type_valid and pattern_valid) else 0.3
                final_confidence = min(confidence * validation_score, 1.0)

                field_scores[field_name] = final_confidence
            else:
                # Missing field
                field_scores[field_name] = 0.0 if field.required else 0.1

        return field_scores

    def _validate_data_type(self, value: Any, data_type: DataType) -> bool:
        """
        Validate if value matches expected data type

        Args:
            value: Value to validate
            data_type: Expected data type

        Returns:
            True if value matches data type
        """
        if value is None:
            return True

        value_str = str(value).strip()

        if data_type == DataType.EMAIL:
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            return bool(re.match(email_pattern, value_str))

        elif data_type == DataType.PHONE:
            # Simple phone validation
            phone_pattern = r'^[\+]?[1-9][\d\s\-\(\)]{7,15}$'
            return bool(re.match(phone_pattern, value_str))

        elif data_type == DataType.URL:
            url_pattern = r'^https?://[^\s/$.?#].[^\s]*$'
            return bool(re.match(url_pattern, value_str))

        elif data_type == DataType.DATE:
            # Basic date pattern validation
            date_patterns = [
                r'\d{4}-\d{2}-\d{2}',  # YYYY-MM-DD
                r'\d{2}/\d{2}/\d{4}',  # MM/DD/YYYY
                r'\d{2}-\d{2}-\d{4}',  # MM-DD-YYYY
            ]
            return any(re.match(pattern, value_str) for pattern in date_patterns)

        elif data_type == DataType.NUMBER:
            try:
                float(value_str.replace(',', ''))
                return True
            except ValueError:
                return False

        elif data_type == DataType.CURRENCY:
            currency_pattern = r'^[\$€£¥]?[\d,]+\.?\d*$'
            return bool(re.match(currency_pattern, value_str))

        elif data_type == DataType.PERCENTAGE:
            percentage_pattern = r'^\d+\.?\d*%?$'
            return bool(re.match(percentage_pattern, value_str))

        # For TEXT, NAME, ADDRESS - always valid if not empty
        return len(value_str) > 0

    def _validate_pattern(self, value: Any, pattern: Optional[str]) -> bool:
        """
        Validate value against custom pattern

        Args:
            value: Value to validate
            pattern: Regex pattern to match

        Returns:
            True if value matches pattern or no pattern provided
        """
        if not pattern or value is None:
            return True

        try:
            return bool(re.match(pattern, str(value)))
        except re.error:
            logger.warning(f"Invalid regex pattern: {pattern}")
            return True

    async def match_data(
        self, text: str, schema_name: Optional[str] = None
    ) -> MatchResult:
        """
        Match and extract structured data from text

        Args:
            text: Text to extract data from
            schema_name: Specific schema to use (if None, tries all schemas)

        Returns:
            MatchResult containing extracted and validated data
        """
        if not self.schemas:
            return MatchResult(
                matched_data={},
                confidence_score=0.0,
                schema_name="",
                field_matches={},
                raw_extractions={},
                success=False,
                error_message="No schemas available"
            )

        # Determine schemas to try
        schemas_to_try = (
            [self.schemas[schema_name]] if schema_name and schema_name in self.schemas
            else list(self.schemas.values())
        )

        best_result = None
        best_score = 0.0

        for schema in schemas_to_try:
            try:
                # Extract data using AI
                raw_data = await self._extract_with_ai(text, schema)

                # Validate and score the extraction
                field_scores = self._validate_extracted_data(raw_data, schema)

                # Calculate overall confidence
                total_score = sum(field_scores.values())
                max_possible = len(schema.fields)
                confidence = total_score / max(max_possible, 1)

                # Process extracted data
                matched_data = {}
                raw_extractions = {}

                for field in schema.fields:
                    field_name = field.name
                    if field_name in raw_data:
                        field_data = raw_data[field_name]

                        if isinstance(field_data, dict) and 'value' in field_data:
                            matched_data[field_name] = field_data['value']
                        else:
                            matched_data[field_name] = field_data

                        raw_extractions[field_name] = [str(matched_data[field_name])]
                    else:
                        matched_data[field_name] = None
                        raw_extractions[field_name] = []

                result = MatchResult(
                    matched_data=matched_data,
                    confidence_score=confidence,
                    schema_name=schema.name,
                    field_matches=field_scores,
                    raw_extractions=raw_extractions,
                    success=True
                )

                # Keep best result
                if confidence > best_score:
                    best_result = result
                    best_score = confidence

            except Exception as e:
                logger.error(f"Error matching with schema {schema.name}: {e}")
                continue

        if best_result:
            return best_result
        else:
            return MatchResult(
                matched_data={},
                confidence_score=0.0,
                schema_name="",
                field_matches={},
                raw_extractions={},
                success=False,
                error_message="No successful matches found"
            )

    async def batch_match(
        self, texts: List[str], schema_name: Optional[str] = None
    ) -> List[MatchResult]:
        """
        Match multiple texts concurrently

        Args:
            texts: List of texts to process
            schema_name: Specific schema to use

        Returns:
            List of MatchResult objects
        """
        tasks = [self.match_data(text, schema_name) for text in texts]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Handle exceptions
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append(MatchResult(
                    matched_data={},
                    confidence_score=0.0,
                    schema_name=schema_name or "",
                    field_matches={},
                    raw_extractions={},
                    success=False,
                    error_message=f"Exception during matching: {str(result)}"
                ))
            else:
                processed_results.append(result)

        return processed_results

    def create_default_schemas(self) -> None:
        """Create some default schemas for common data types"""

        # Contact Information Schema
        contact_schema = DataSchema(
            name="contact_info",
            description="Extract contact information from text",
            fields=[
                FieldSchema(
                    name="name",
                    data_type=DataType.NAME,
                    description="Person's full name",
                    required=True,
                    examples=["John Smith", "Jane Doe", "Dr. Robert Johnson"]
                ),
                FieldSchema(
                    name="email",
                    data_type=DataType.EMAIL,
                    description="Email address",
                    required=False,
                    examples=["<EMAIL>", "<EMAIL>"]
                ),
                FieldSchema(
                    name="phone",
                    data_type=DataType.PHONE,
                    description="Phone number",
                    required=False,
                    examples=["******-123-4567", "(*************", "************"]
                ),
                FieldSchema(
                    name="address",
                    data_type=DataType.ADDRESS,
                    description="Physical address",
                    required=False,
                    examples=["123 Main St, City, State 12345"]
                )
            ]
        )

        # Financial Data Schema
        financial_schema = DataSchema(
            name="financial_data",
            description="Extract financial information from text",
            fields=[
                FieldSchema(
                    name="amount",
                    data_type=DataType.CURRENCY,
                    description="Monetary amount",
                    required=True,
                    examples=["$1,234.56", "€500.00", "£99.99"]
                ),
                FieldSchema(
                    name="date",
                    data_type=DataType.DATE,
                    description="Transaction or report date",
                    required=False,
                    examples=["2024-01-15", "01/15/2024", "15-01-2024"]
                ),
                FieldSchema(
                    name="description",
                    data_type=DataType.TEXT,
                    description="Transaction description",
                    required=False,
                    examples=["Payment for services", "Monthly subscription"]
                ),
                FieldSchema(
                    name="percentage",
                    data_type=DataType.PERCENTAGE,
                    description="Percentage value (interest, tax, etc.)",
                    required=False,
                    examples=["5.5%", "12.5", "0.75%"]
                )
            ]
        )

        # Add schemas
        self.add_schema(contact_schema)
        self.add_schema(financial_schema)

        logger.info("Created default schemas: contact_info, financial_data")

    def export_schema(self, schema_name: str) -> Optional[Dict[str, Any]]:
        """
        Export schema to dictionary format

        Args:
            schema_name: Name of schema to export

        Returns:
            Schema as dictionary or None if not found
        """
        schema = self.get_schema(schema_name)
        if not schema:
            return None

        return {
            "name": schema.name,
            "description": schema.description,
            "fields": [
                {
                    "name": field.name,
                    "data_type": field.data_type.value,
                    "description": field.description,
                    "required": field.required,
                    "pattern": field.pattern,
                    "examples": field.examples,
                    "validation_rules": field.validation_rules
                }
                for field in schema.fields
            ],
            "metadata": schema.metadata
        }

    def import_schema(self, schema_dict: Dict[str, Any]) -> bool:
        """
        Import schema from dictionary format

        Args:
            schema_dict: Schema as dictionary

        Returns:
            True if import successful
        """
        try:
            fields = []
            for field_dict in schema_dict.get("fields", []):
                field = FieldSchema(
                    name=field_dict["name"],
                    data_type=DataType(field_dict["data_type"]),
                    description=field_dict["description"],
                    required=field_dict.get("required", False),
                    pattern=field_dict.get("pattern"),
                    examples=field_dict.get("examples", []),
                    validation_rules=field_dict.get("validation_rules", {})
                )
                fields.append(field)

            schema = DataSchema(
                name=schema_dict["name"],
                description=schema_dict["description"],
                fields=fields,
                metadata=schema_dict.get("metadata", {})
            )

            self.add_schema(schema)
            return True

        except Exception as e:
            logger.error(f"Error importing schema: {e}")
            return False