"""
Error Handler Module

This module provides comprehensive error handling, logging, and user feedback
mechanisms for the DataEntryAI application.
"""

import logging
import traceback
import functools
from datetime import datetime
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Union
from dataclasses import dataclass
from enum import Enum

import streamlit as st


class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Error categories"""
    FILE_PROCESSING = "file_processing"
    WEB_CRAWLING = "web_crawling"
    AI_PROCESSING = "ai_processing"
    DATA_EXPORT = "data_export"
    CONFIGURATION = "configuration"
    NETWORK = "network"
    VALIDATION = "validation"
    SYSTEM = "system"


@dataclass
class ErrorInfo:
    """Error information container"""
    error_id: str
    category: ErrorCategory
    severity: ErrorSeverity
    message: str
    details: Optional[str] = None
    timestamp: Optional[datetime] = None
    context: Optional[Dict[str, Any]] = None
    traceback_info: Optional[str] = None
    user_message: Optional[str] = None
    recovery_suggestions: Optional[List[str]] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
        if self.context is None:
            self.context = {}
        if self.recovery_suggestions is None:
            self.recovery_suggestions = []


class ErrorHandler:
    """
    Comprehensive error handling system
    """
    
    def __init__(self, log_file: Optional[str] = None):
        """
        Initialize error handler
        
        Args:
            log_file: Path to log file (optional)
        """
        self.log_file = log_file
        self.errors: List[ErrorInfo] = []
        self.setup_logging()
    
    def setup_logging(self) -> None:
        """Setup logging configuration"""
        # Create logs directory if it doesn't exist
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # Configure logging
        log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        
        # File handler
        if self.log_file:
            file_handler = logging.FileHandler(self.log_file)
            file_handler.setLevel(logging.DEBUG)
            file_handler.setFormatter(logging.Formatter(log_format))
        else:
            # Default log file
            log_file = log_dir / f"dataentry_{datetime.now().strftime('%Y%m%d')}.log"
            file_handler = logging.FileHandler(log_file)
            file_handler.setLevel(logging.DEBUG)
            file_handler.setFormatter(logging.Formatter(log_format))
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(logging.Formatter(log_format))
        
        # Root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.DEBUG)
        root_logger.addHandler(file_handler)
        root_logger.addHandler(console_handler)
    
    def handle_error(
        self,
        error: Exception,
        category: ErrorCategory,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        context: Optional[Dict[str, Any]] = None,
        user_message: Optional[str] = None,
        recovery_suggestions: Optional[List[str]] = None
    ) -> ErrorInfo:
        """
        Handle an error and create error info
        
        Args:
            error: The exception that occurred
            category: Error category
            severity: Error severity level
            context: Additional context information
            user_message: User-friendly error message
            recovery_suggestions: List of recovery suggestions
            
        Returns:
            ErrorInfo object
        """
        error_id = f"{category.value}_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
        
        error_info = ErrorInfo(
            error_id=error_id,
            category=category,
            severity=severity,
            message=str(error),
            details=traceback.format_exc(),
            context=context or {},
            traceback_info=traceback.format_exc(),
            user_message=user_message or self._generate_user_message(error, category),
            recovery_suggestions=recovery_suggestions or self._generate_recovery_suggestions(error, category)
        )
        
        self.errors.append(error_info)
        self._log_error(error_info)
        
        return error_info
    
    def _generate_user_message(self, error: Exception, category: ErrorCategory) -> str:
        """Generate user-friendly error message"""
        base_messages = {
            ErrorCategory.FILE_PROCESSING: "There was an issue processing your file.",
            ErrorCategory.WEB_CRAWLING: "Unable to crawl the specified website.",
            ErrorCategory.AI_PROCESSING: "AI processing encountered an error.",
            ErrorCategory.DATA_EXPORT: "Failed to export data.",
            ErrorCategory.CONFIGURATION: "Configuration error detected.",
            ErrorCategory.NETWORK: "Network connection issue.",
            ErrorCategory.VALIDATION: "Data validation failed.",
            ErrorCategory.SYSTEM: "System error occurred."
        }
        
        base_message = base_messages.get(category, "An unexpected error occurred.")
        
        # Add specific error details for common issues
        error_str = str(error).lower()
        
        if "api key" in error_str or "authentication" in error_str:
            return f"{base_message} Please check your API key configuration."
        elif "network" in error_str or "connection" in error_str:
            return f"{base_message} Please check your internet connection."
        elif "file not found" in error_str:
            return f"{base_message} The specified file could not be found."
        elif "permission" in error_str:
            return f"{base_message} Permission denied. Please check file permissions."
        
        return base_message
    
    def _generate_recovery_suggestions(self, error: Exception, category: ErrorCategory) -> List[str]:
        """Generate recovery suggestions"""
        suggestions = []
        error_str = str(error).lower()
        
        # Common suggestions based on error type
        if "api key" in error_str:
            suggestions.extend([
                "Check that your API key is correctly set in environment variables",
                "Verify that your API key is valid and has not expired",
                "Ensure you have sufficient API credits"
            ])
        
        elif "network" in error_str or "connection" in error_str:
            suggestions.extend([
                "Check your internet connection",
                "Try again in a few moments",
                "Verify that the target URL is accessible"
            ])
        
        elif "file" in error_str:
            suggestions.extend([
                "Verify that the file exists and is accessible",
                "Check file permissions",
                "Ensure the file format is supported"
            ])
        
        # Category-specific suggestions
        if category == ErrorCategory.FILE_PROCESSING:
            suggestions.extend([
                "Try a different file format",
                "Check if the file is corrupted",
                "Reduce file size if it's too large"
            ])
        
        elif category == ErrorCategory.WEB_CRAWLING:
            suggestions.extend([
                "Check if the website allows crawling (robots.txt)",
                "Reduce crawling speed to avoid rate limiting",
                "Verify the URL format is correct"
            ])
        
        elif category == ErrorCategory.AI_PROCESSING:
            suggestions.extend([
                "Try with a simpler text input",
                "Check if the AI service is available",
                "Reduce the amount of text being processed"
            ])
        
        return suggestions
    
    def _log_error(self, error_info: ErrorInfo) -> None:
        """Log error information"""
        logger = logging.getLogger(__name__)
        
        log_message = f"[{error_info.error_id}] {error_info.category.value.upper()}: {error_info.message}"
        
        if error_info.severity == ErrorSeverity.CRITICAL:
            logger.critical(log_message)
        elif error_info.severity == ErrorSeverity.HIGH:
            logger.error(log_message)
        elif error_info.severity == ErrorSeverity.MEDIUM:
            logger.warning(log_message)
        else:
            logger.info(log_message)
        
        # Log additional details
        if error_info.context:
            logger.debug(f"Context: {error_info.context}")
        
        if error_info.traceback_info:
            logger.debug(f"Traceback: {error_info.traceback_info}")
    
    def display_error_in_streamlit(self, error_info: ErrorInfo) -> None:
        """Display error in Streamlit interface"""
        if error_info.severity == ErrorSeverity.CRITICAL:
            st.error(f"🚨 Critical Error: {error_info.user_message}")
        elif error_info.severity == ErrorSeverity.HIGH:
            st.error(f"❌ Error: {error_info.user_message}")
        elif error_info.severity == ErrorSeverity.MEDIUM:
            st.warning(f"⚠️ Warning: {error_info.user_message}")
        else:
            st.info(f"ℹ️ Info: {error_info.user_message}")
        
        # Show recovery suggestions
        if error_info.recovery_suggestions:
            with st.expander("💡 Suggestions to resolve this issue"):
                for suggestion in error_info.recovery_suggestions:
                    st.write(f"• {suggestion}")
        
        # Show error ID for support
        st.caption(f"Error ID: {error_info.error_id}")
    
    def get_error_summary(self) -> Dict[str, Any]:
        """Get summary of all errors"""
        if not self.errors:
            return {"total": 0, "by_category": {}, "by_severity": {}}
        
        by_category = {}
        by_severity = {}
        
        for error in self.errors:
            # Count by category
            category = error.category.value
            by_category[category] = by_category.get(category, 0) + 1
            
            # Count by severity
            severity = error.severity.value
            by_severity[severity] = by_severity.get(severity, 0) + 1
        
        return {
            "total": len(self.errors),
            "by_category": by_category,
            "by_severity": by_severity,
            "recent_errors": self.errors[-5:] if len(self.errors) > 5 else self.errors
        }
    
    def clear_errors(self) -> None:
        """Clear all stored errors"""
        self.errors.clear()


def error_handler_decorator(
    category: ErrorCategory,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    user_message: Optional[str] = None,
    recovery_suggestions: Optional[List[str]] = None,
    reraise: bool = False
):
    """
    Decorator for automatic error handling
    
    Args:
        category: Error category
        severity: Error severity level
        user_message: Custom user message
        recovery_suggestions: Custom recovery suggestions
        reraise: Whether to reraise the exception after handling
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Get error handler from session state or create new one
                if hasattr(st, 'session_state') and 'error_handler' in st.session_state:
                    handler = st.session_state.error_handler
                else:
                    handler = ErrorHandler()
                
                error_info = handler.handle_error(
                    e, category, severity, 
                    user_message=user_message,
                    recovery_suggestions=recovery_suggestions
                )
                
                # Display in Streamlit if available
                try:
                    handler.display_error_in_streamlit(error_info)
                except:
                    pass  # Streamlit not available
                
                if reraise:
                    raise
                
                return None
        
        return wrapper
    return decorator


# Global error handler instance
global_error_handler = ErrorHandler()
