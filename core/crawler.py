"""
Web Crawler Module

This module provides asynchronous web crawling capabilities with rate limiting,
error handling, and configurable crawling parameters.
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Set, Any
from urllib.parse import urljoin, urlparse
from dataclasses import dataclass, field

import aiohttp
from bs4 import BeautifulSoup
import aiofiles

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class CrawlResult:
    """Result of a web crawling operation"""
    url: str
    content: str
    title: Optional[str]
    metadata: Dict[str, Any]
    links: List[str]
    success: bool
    status_code: Optional[int] = None
    error_message: Optional[str] = None
    crawl_time: float = 0.0


@dataclass
class CrawlConfig:
    """Configuration for web crawling operations"""
    max_pages: int = 10
    max_depth: int = 2
    delay_between_requests: float = 1.0
    timeout: int = 30
    max_concurrent: int = 5
    follow_external_links: bool = False
    allowed_domains: Optional[List[str]] = None
    blocked_domains: Optional[List[str]] = None
    user_agent: str = "DataEntryAI-Crawler/1.0"
    headers: Dict[str, str] = field(default_factory=dict)
    respect_robots_txt: bool = True


class WebCrawler:
    """
    Asynchronous web crawler with rate limiting and error handling
    """

    def __init__(self, config: Optional[CrawlConfig] = None):
        """
        Initialize the web crawler

        Args:
            config: Crawling configuration
        """
        self.config = config or CrawlConfig()
        self.session: Optional[aiohttp.ClientSession] = None
        self.crawled_urls: Set[str] = set()
        self.failed_urls: Set[str] = set()
        self.semaphore = asyncio.Semaphore(self.config.max_concurrent)
        self.last_request_time = 0.0

    async def __aenter__(self):
        """Async context manager entry"""
        await self.start_session()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close_session()

    async def start_session(self):
        """Start the aiohttp session"""
        headers = {
            'User-Agent': self.config.user_agent,
            **self.config.headers
        }

        timeout = aiohttp.ClientTimeout(total=self.config.timeout)
        self.session = aiohttp.ClientSession(
            headers=headers,
            timeout=timeout
        )

    async def close_session(self):
        """Close the aiohttp session"""
        if self.session:
            await self.session.close()

    def _is_valid_url(self, url: str, base_domain: Optional[str] = None) -> bool:
        """
        Check if URL is valid for crawling

        Args:
            url: URL to validate
            base_domain: Base domain for internal link checking

        Returns:
            True if URL is valid for crawling
        """
        try:
            parsed = urlparse(url)

            # Check if URL has valid scheme
            if parsed.scheme not in ['http', 'https']:
                return False

            # Check blocked domains
            if self.config.blocked_domains:
                for blocked in self.config.blocked_domains:
                    if blocked in parsed.netloc:
                        return False

            # Check allowed domains
            if self.config.allowed_domains:
                allowed = any(
                    domain in parsed.netloc
                    for domain in self.config.allowed_domains
                )
                if not allowed:
                    return False

            # Check external links policy
            if not self.config.follow_external_links and base_domain:
                if parsed.netloc != base_domain:
                    return False

            return True

        except Exception as e:
            logger.warning(f"Error validating URL {url}: {e}")
            return False

    async def _rate_limit(self):
        """Apply rate limiting between requests"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time

        if time_since_last < self.config.delay_between_requests:
            sleep_time = self.config.delay_between_requests - time_since_last
            await asyncio.sleep(sleep_time)

        self.last_request_time = time.time()

    async def _fetch_page(self, url: str) -> CrawlResult:
        """
        Fetch a single page

        Args:
            url: URL to fetch

        Returns:
            CrawlResult containing page data
        """
        start_time = time.time()

        async with self.semaphore:
            await self._rate_limit()

            try:
                if not self.session:
                    await self.start_session()

                async with self.session.get(url) as response:
                    content = await response.text()

                    # Parse HTML content
                    soup = BeautifulSoup(content, 'html.parser')

                    # Extract title
                    title_tag = soup.find('title')
                    title = title_tag.get_text().strip() if title_tag else None

                    # Extract text content
                    # Remove script and style elements
                    for script in soup(["script", "style"]):
                        script.decompose()

                    text_content = soup.get_text()
                    # Clean up text
                    lines = (line.strip() for line in text_content.splitlines())
                    chunks = (phrase.strip() for line in lines
                             for phrase in line.split("  "))
                    text_content = ' '.join(chunk for chunk in chunks if chunk)

                    # Extract links
                    links = []
                    for link in soup.find_all('a', href=True):
                        absolute_url = urljoin(url, link['href'])
                        links.append(absolute_url)

                    # Metadata
                    metadata = {
                        'content_length': len(content),
                        'text_length': len(text_content),
                        'links_count': len(links),
                        'response_headers': dict(response.headers),
                        'content_type': response.headers.get('content-type', '')
                    }

                    crawl_time = time.time() - start_time

                    return CrawlResult(
                        url=url,
                        content=text_content,
                        title=title,
                        metadata=metadata,
                        links=links,
                        success=True,
                        status_code=response.status,
                        crawl_time=crawl_time
                    )

            except Exception as e:
                logger.error(f"Error fetching {url}: {e}")
                crawl_time = time.time() - start_time

                return CrawlResult(
                    url=url,
                    content="",
                    title=None,
                    metadata={},
                    links=[],
                    success=False,
                    error_message=str(e),
                    crawl_time=crawl_time
                )

    async def crawl_single_page(self, url: str) -> CrawlResult:
        """
        Crawl a single page

        Args:
            url: URL to crawl

        Returns:
            CrawlResult containing page data
        """
        if not self.session:
            await self.start_session()

        result = await self._fetch_page(url)
        self.crawled_urls.add(url)

        if not result.success:
            self.failed_urls.add(url)

        return result

    async def crawl_website(
        self, start_url: str, max_pages: Optional[int] = None
    ) -> List[CrawlResult]:
        """
        Crawl a website starting from a given URL

        Args:
            start_url: Starting URL for crawling
            max_pages: Maximum number of pages to crawl (overrides config)

        Returns:
            List of CrawlResult objects
        """
        if not self.session:
            await self.start_session()

        max_pages = max_pages or self.config.max_pages
        base_domain = urlparse(start_url).netloc

        # Initialize crawling queue
        to_crawl = [(start_url, 0)]  # (url, depth)
        results = []

        while to_crawl and len(results) < max_pages:
            current_url, depth = to_crawl.pop(0)

            # Skip if already crawled or depth exceeded
            if (current_url in self.crawled_urls or
                depth > self.config.max_depth):
                continue

            # Validate URL
            if not self._is_valid_url(current_url, base_domain):
                continue

            # Crawl the page
            result = await self._fetch_page(current_url)
            results.append(result)
            self.crawled_urls.add(current_url)

            if not result.success:
                self.failed_urls.add(current_url)
                continue

            # Add new links to crawl queue if within depth limit
            if depth < self.config.max_depth:
                for link in result.links:
                    if (link not in self.crawled_urls and
                        self._is_valid_url(link, base_domain)):
                        to_crawl.append((link, depth + 1))

        return results

    async def crawl_multiple_urls(self, urls: List[str]) -> List[CrawlResult]:
        """
        Crawl multiple URLs concurrently

        Args:
            urls: List of URLs to crawl

        Returns:
            List of CrawlResult objects
        """
        if not self.session:
            await self.start_session()

        # Filter valid URLs
        valid_urls = [url for url in urls if self._is_valid_url(url)]

        # Create tasks for concurrent crawling
        tasks = [self._fetch_page(url) for url in valid_urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Process results and handle exceptions
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append(CrawlResult(
                    url=valid_urls[i],
                    content="",
                    title=None,
                    metadata={},
                    links=[],
                    success=False,
                    error_message=f"Exception during crawling: {str(result)}",
                    crawl_time=0.0
                ))
            else:
                processed_results.append(result)
                self.crawled_urls.add(result.url)
                if not result.success:
                    self.failed_urls.add(result.url)

        return processed_results

    async def save_results(
        self, results: List[CrawlResult], output_file: str
    ) -> None:
        """
        Save crawl results to a file

        Args:
            results: List of crawl results
            output_file: Path to output file
        """
        import json

        # Convert results to serializable format
        serializable_results = []
        for result in results:
            serializable_results.append({
                'url': result.url,
                'content': result.content,
                'title': result.title,
                'metadata': result.metadata,
                'links': result.links,
                'success': result.success,
                'status_code': result.status_code,
                'error_message': result.error_message,
                'crawl_time': result.crawl_time
            })

        # Save to file
        async with aiofiles.open(output_file, 'w', encoding='utf-8') as f:
            await f.write(json.dumps(serializable_results, indent=2))

        logger.info(f"Saved {len(results)} crawl results to {output_file}")

    def get_statistics(self) -> Dict[str, Any]:
        """
        Get crawling statistics

        Returns:
            Dictionary containing crawling statistics
        """
        return {
            'total_crawled': len(self.crawled_urls),
            'total_failed': len(self.failed_urls),
            'success_rate': (
                len(self.crawled_urls) - len(self.failed_urls)
            ) / max(len(self.crawled_urls), 1),
            'crawled_urls': list(self.crawled_urls),
            'failed_urls': list(self.failed_urls)
        }