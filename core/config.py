"""
Configuration Manager Module

This module provides configuration management for the DataEntryAI application,
including loading YAML configurations, environment variable support, and validation.
"""

import os
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass

import yaml
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class AppConfig:
    """Application configuration"""
    name: str = "DataEntryAI"
    version: str = "1.0.0"
    debug: bool = False
    log_level: str = "INFO"
    max_file_size_mb: int = 50
    supported_formats: List[str] = None

    def __post_init__(self):
        if self.supported_formats is None:
            self.supported_formats = ["pdf", "docx", "txt", "csv", "png", "jpg", "jpeg"]


@dataclass
class AIConfig:
    """AI configuration"""
    provider: str = "groq"
    model: str = "llama3-8b-8192"
    api_key_env: str = "GROQ_API_KEY"
    temperature: float = 0.1
    max_tokens: int = 2000
    timeout: int = 30


@dataclass
class CrawlerConfig:
    """Web crawler configuration"""
    max_pages: int = 10
    max_depth: int = 2
    delay_between_requests: float = 1.0
    timeout: int = 30
    max_concurrent: int = 5
    follow_external_links: bool = False
    user_agent: str = "DataEntryAI-Crawler/1.0"
    respect_robots_txt: bool = True


@dataclass
class OCRConfig:
    """OCR configuration"""
    tesseract_config: str = "--psm 6"
    languages: List[str] = None
    confidence_threshold: int = 60

    def __post_init__(self):
        if self.languages is None:
            self.languages = ["eng"]


@dataclass
class ExportConfig:
    """Export configuration"""
    default_format: str = "xlsx"
    include_metadata: bool = True
    include_summary: bool = True
    auto_format: bool = True


@dataclass
class JobConfig:
    """Job configuration"""
    name: str
    description: str
    enabled: bool = True
    schema: Optional[str] = None
    sources: List[Dict[str, Any]] = None
    output: Dict[str, Any] = None
    validation: Dict[str, Any] = None
    ocr: Dict[str, Any] = None

    def __post_init__(self):
        if self.sources is None:
            self.sources = []
        if self.output is None:
            self.output = {}
        if self.validation is None:
            self.validation = {}
        if self.ocr is None:
            self.ocr = {}


@dataclass
class SchemaFieldConfig:
    """Schema field configuration"""
    name: str
    type: str
    description: str
    required: bool = False
    pattern: Optional[str] = None
    examples: List[str] = None

    def __post_init__(self):
        if self.examples is None:
            self.examples = []


@dataclass
class SchemaConfig:
    """Schema configuration"""
    name: str
    description: str
    fields: List[SchemaFieldConfig]


class ConfigManager:
    """
    Configuration manager for the DataEntryAI application
    """
    
    def __init__(self, config_file: str = "jobs.yaml"):
        """
        Initialize the configuration manager
        
        Args:
            config_file: Path to the configuration file
        """
        self.config_file = Path(config_file)
        self.config_data: Dict[str, Any] = {}
        self.app_config: Optional[AppConfig] = None
        self.ai_config: Optional[AIConfig] = None
        self.crawler_config: Optional[CrawlerConfig] = None
        self.ocr_config: Optional[OCRConfig] = None
        self.export_config: Optional[ExportConfig] = None
        self.jobs: Dict[str, JobConfig] = {}
        self.schemas: Dict[str, SchemaConfig] = {}
        
        self.load_config()
    
    def load_config(self) -> None:
        """Load configuration from YAML file"""
        try:
            if not self.config_file.exists():
                logger.warning(f"Config file {self.config_file} not found. Using defaults.")
                self._create_default_config()
                return
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config_data = yaml.safe_load(f)
            
            self._parse_config()
            logger.info(f"Loaded configuration from {self.config_file}")
            
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            self._create_default_config()
    
    def _parse_config(self) -> None:
        """Parse loaded configuration data"""
        # Parse app config
        app_data = self.config_data.get("app", {})
        self.app_config = AppConfig(**app_data)
        
        # Parse AI config
        ai_data = self.config_data.get("ai", {})
        self.ai_config = AIConfig(**ai_data)
        
        # Parse crawler config
        crawler_data = self.config_data.get("crawler", {})
        self.crawler_config = CrawlerConfig(**crawler_data)
        
        # Parse OCR config
        ocr_data = self.config_data.get("ocr", {})
        self.ocr_config = OCRConfig(**ocr_data)
        
        # Parse export config
        export_data = self.config_data.get("export", {})
        self.export_config = ExportConfig(**export_data)
        
        # Parse jobs
        jobs_data = self.config_data.get("jobs", {})
        for job_name, job_config in jobs_data.items():
            self.jobs[job_name] = JobConfig(**job_config)
        
        # Parse schemas
        schemas_data = self.config_data.get("schemas", {})
        for schema_name, schema_config in schemas_data.items():
            fields = []
            for field_data in schema_config.get("fields", []):
                fields.append(SchemaFieldConfig(**field_data))
            
            self.schemas[schema_name] = SchemaConfig(
                name=schema_config["name"],
                description=schema_config["description"],
                fields=fields
            )
    
    def _create_default_config(self) -> None:
        """Create default configuration"""
        self.app_config = AppConfig()
        self.ai_config = AIConfig()
        self.crawler_config = CrawlerConfig()
        self.ocr_config = OCRConfig()
        self.export_config = ExportConfig()
        self.jobs = {}
        self.schemas = {}
        
        logger.info("Created default configuration")
    
    def get_job_config(self, job_name: str) -> Optional[JobConfig]:
        """
        Get job configuration by name
        
        Args:
            job_name: Name of the job
            
        Returns:
            JobConfig if found, None otherwise
        """
        return self.jobs.get(job_name)
    
    def get_schema_config(self, schema_name: str) -> Optional[SchemaConfig]:
        """
        Get schema configuration by name
        
        Args:
            schema_name: Name of the schema
            
        Returns:
            SchemaConfig if found, None otherwise
        """
        return self.schemas.get(schema_name)
    
    def list_jobs(self) -> List[str]:
        """
        List all available job names
        
        Returns:
            List of job names
        """
        return list(self.jobs.keys())
    
    def list_schemas(self) -> List[str]:
        """
        List all available schema names
        
        Returns:
            List of schema names
        """
        return list(self.schemas.keys())
    
    def get_api_key(self) -> Optional[str]:
        """
        Get API key from environment variables
        
        Returns:
            API key if found, None otherwise
        """
        if self.ai_config:
            return os.getenv(self.ai_config.api_key_env)
        return None
    
    def validate_config(self) -> List[str]:
        """
        Validate configuration and return list of issues
        
        Returns:
            List of validation issues (empty if valid)
        """
        issues = []
        
        # Check API key
        if not self.get_api_key():
            issues.append(f"API key not found in environment variable: {self.ai_config.api_key_env}")
        
        # Validate jobs
        for job_name, job_config in self.jobs.items():
            if job_config.schema and job_config.schema not in self.schemas:
                issues.append(f"Job '{job_name}' references unknown schema: {job_config.schema}")
        
        return issues
    
    def save_config(self, filename: Optional[str] = None) -> None:
        """
        Save current configuration to file
        
        Args:
            filename: Output filename (uses current config file if None)
        """
        output_file = Path(filename) if filename else self.config_file
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                yaml.dump(self.config_data, f, default_flow_style=False, indent=2)
            
            logger.info(f"Saved configuration to {output_file}")
            
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")
