"""
Excel Writer Module

This module provides robust Excel export capabilities with proper formatting,
multiple sheets, data validation, and advanced features.
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field

from openpyxl import Workbook
from openpyxl.styles import (
    Font, PatternFill, Border, Side, Alignment, NamedStyle
)
from openpyxl.utils import get_column_letter
from openpyxl.worksheet.datavalidation import DataValidation
from openpyxl.chart import BarChart, Reference
from openpyxl.formatting.rule import ColorScaleRule

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class CellStyle:
    """Style configuration for Excel cells"""
    font_name: str = "Cal<PERSON>ri"
    font_size: int = 11
    font_bold: bool = False
    font_color: str = "000000"
    background_color: Optional[str] = None
    border_style: str = "thin"
    border_color: str = "000000"
    alignment_horizontal: str = "left"
    alignment_vertical: str = "center"
    number_format: Optional[str] = None


@dataclass
class ColumnConfig:
    """Configuration for Excel columns"""
    name: str
    width: Optional[float] = None
    style: Optional[CellStyle] = None
    data_validation: Optional[str] = None
    formula: Optional[str] = None


@dataclass
class SheetConfig:
    """Configuration for Excel sheets"""
    name: str
    columns: List[ColumnConfig]
    data: List[Dict[str, Any]]
    header_style: Optional[CellStyle] = None
    data_style: Optional[CellStyle] = None
    freeze_panes: Optional[str] = None
    auto_filter: bool = True
    conditional_formatting: Dict[str, Any] = field(default_factory=dict)


class ExcelWriter:
    """
    Robust Excel export system with advanced formatting and features
    """

    def __init__(self):
        """Initialize the Excel writer"""
        self.workbook: Optional[Workbook] = None
        self.default_styles = self._create_default_styles()

    def _create_default_styles(self) -> Dict[str, NamedStyle]:
        """Create default named styles for Excel"""
        styles = {}

        # Header style
        header_style = NamedStyle(name="header")
        header_style.font = Font(bold=True, color="FFFFFF", size=12)
        header_style.fill = PatternFill(
            start_color="366092", end_color="366092", fill_type="solid"
        )
        header_style.border = Border(
            left=Side(style="thin"),
            right=Side(style="thin"),
            top=Side(style="thin"),
            bottom=Side(style="thin")
        )
        header_style.alignment = Alignment(
            horizontal="center", vertical="center"
        )
        styles["header"] = header_style

        # Data style
        data_style = NamedStyle(name="data")
        data_style.font = Font(size=11)
        data_style.border = Border(
            left=Side(style="thin"),
            right=Side(style="thin"),
            top=Side(style="thin"),
            bottom=Side(style="thin")
        )
        data_style.alignment = Alignment(vertical="center")
        styles["data"] = data_style

        # Number style
        number_style = NamedStyle(name="number")
        number_style.font = Font(size=11)
        number_style.number_format = "#,##0.00"
        number_style.alignment = Alignment(horizontal="right", vertical="center")
        styles["number"] = number_style

        # Date style
        date_style = NamedStyle(name="date")
        date_style.font = Font(size=11)
        date_style.number_format = "yyyy-mm-dd"
        date_style.alignment = Alignment(horizontal="center", vertical="center")
        styles["date"] = date_style

        # Percentage style
        percentage_style = NamedStyle(name="percentage")
        percentage_style.font = Font(size=11)
        percentage_style.number_format = "0.00%"
        percentage_style.alignment = Alignment(horizontal="right", vertical="center")
        styles["percentage"] = percentage_style

        return styles

    def create_workbook(self) -> Workbook:
        """
        Create a new workbook

        Returns:
            New Workbook instance
        """
        self.workbook = Workbook()

        # Add default styles to workbook
        for style in self.default_styles.values():
            self.workbook.add_named_style(style)

        # Remove default sheet
        if "Sheet" in self.workbook.sheetnames:
            self.workbook.remove(self.workbook["Sheet"])

        return self.workbook

    def _apply_cell_style(self, cell, style: CellStyle) -> None:
        """
        Apply style to a cell

        Args:
            cell: Excel cell object
            style: CellStyle to apply
        """
        # Font
        cell.font = Font(
            name=style.font_name,
            size=style.font_size,
            bold=style.font_bold,
            color=style.font_color
        )

        # Fill
        if style.background_color:
            cell.fill = PatternFill(
                start_color=style.background_color,
                end_color=style.background_color,
                fill_type="solid"
            )

        # Border
        cell.border = Border(
            left=Side(style=style.border_style, color=style.border_color),
            right=Side(style=style.border_style, color=style.border_color),
            top=Side(style=style.border_style, color=style.border_color),
            bottom=Side(style=style.border_style, color=style.border_color)
        )

        # Alignment
        cell.alignment = Alignment(
            horizontal=style.alignment_horizontal,
            vertical=style.alignment_vertical
        )

        # Number format
        if style.number_format:
            cell.number_format = style.number_format

    def add_sheet(self, sheet_config: SheetConfig) -> None:
        """
        Add a sheet to the workbook

        Args:
            sheet_config: Configuration for the sheet
        """
        if not self.workbook:
            self.create_workbook()

        # Create worksheet
        ws = self.workbook.create_sheet(title=sheet_config.name)

        # Write headers
        for col_idx, column in enumerate(sheet_config.columns, 1):
            cell = ws.cell(row=1, column=col_idx, value=column.name)

            # Apply header style
            if sheet_config.header_style:
                self._apply_cell_style(cell, sheet_config.header_style)
            else:
                cell.style = "header"

        # Write data
        for row_idx, row_data in enumerate(sheet_config.data, 2):
            for col_idx, column in enumerate(sheet_config.columns, 1):
                value = row_data.get(column.name, "")
                cell = ws.cell(row=row_idx, column=col_idx, value=value)

                # Apply data style
                if sheet_config.data_style:
                    self._apply_cell_style(cell, sheet_config.data_style)
                elif column.style:
                    self._apply_cell_style(cell, column.style)
                else:
                    cell.style = "data"

                # Apply column-specific formatting
                if column.formula:
                    cell.value = column.formula

                # Auto-detect and apply number formatting
                self._auto_format_cell(cell, value)

        # Set column widths
        for col_idx, column in enumerate(sheet_config.columns, 1):
            col_letter = get_column_letter(col_idx)
            if column.width:
                ws.column_dimensions[col_letter].width = column.width
            else:
                # Auto-size column
                max_length = len(column.name)
                for row_data in sheet_config.data:
                    value_length = len(str(row_data.get(column.name, "")))
                    max_length = max(max_length, value_length)
                ws.column_dimensions[col_letter].width = min(max_length + 2, 50)

        # Apply freeze panes
        if sheet_config.freeze_panes:
            ws.freeze_panes = sheet_config.freeze_panes

        # Apply auto filter
        if sheet_config.auto_filter and sheet_config.data:
            ws.auto_filter.ref = f"A1:{get_column_letter(len(sheet_config.columns))}{len(sheet_config.data) + 1}"

        # Apply conditional formatting
        self._apply_conditional_formatting(ws, sheet_config)

        logger.info(f"Added sheet '{sheet_config.name}' with {len(sheet_config.data)} rows")

    def _auto_format_cell(self, cell, value: Any) -> None:
        """
        Auto-detect and apply appropriate formatting to cell

        Args:
            cell: Excel cell object
            value: Cell value
        """
        if value is None or value == "":
            return

        value_str = str(value).strip()

        # Check for percentage
        if value_str.endswith('%'):
            try:
                float(value_str[:-1])
                cell.style = "percentage"
                cell.value = float(value_str[:-1]) / 100
                return
            except ValueError:
                pass

        # Check for currency
        if value_str.startswith(('$', '€', '£', '¥')):
            try:
                float(value_str[1:].replace(',', ''))
                cell.style = "number"
                cell.value = float(value_str[1:].replace(',', ''))
                return
            except ValueError:
                pass

        # Check for number
        try:
            if '.' in value_str or ',' in value_str:
                cell.style = "number"
                cell.value = float(value_str.replace(',', ''))
            else:
                cell.value = int(value_str)
            return
        except ValueError:
            pass

        # Check for date
        import re
        date_patterns = [
            r'\d{4}-\d{2}-\d{2}',
            r'\d{2}/\d{2}/\d{4}',
            r'\d{2}-\d{2}-\d{4}'
        ]

        for pattern in date_patterns:
            if re.match(pattern, value_str):
                cell.style = "date"
                try:
                    from datetime import datetime
                    if '-' in value_str and len(value_str.split('-')[0]) == 4:
                        cell.value = datetime.strptime(value_str, '%Y-%m-%d')
                    elif '/' in value_str:
                        cell.value = datetime.strptime(value_str, '%m/%d/%Y')
                    elif '-' in value_str:
                        cell.value = datetime.strptime(value_str, '%m-%d-%Y')
                    return
                except ValueError:
                    pass

    def _apply_conditional_formatting(self, ws, sheet_config: SheetConfig) -> None:
        """
        Apply conditional formatting to worksheet

        Args:
            ws: Worksheet object
            sheet_config: Sheet configuration
        """
        if not sheet_config.conditional_formatting:
            return

        for column_name, formatting_config in sheet_config.conditional_formatting.items():
            # Find column index
            col_idx = None
            for idx, column in enumerate(sheet_config.columns, 1):
                if column.name == column_name:
                    col_idx = idx
                    break

            if col_idx is None:
                continue

            col_letter = get_column_letter(col_idx)
            data_range = f"{col_letter}2:{col_letter}{len(sheet_config.data) + 1}"

            # Apply color scale formatting
            if formatting_config.get("type") == "color_scale":
                rule = ColorScaleRule(
                    start_type="min",
                    start_color=formatting_config.get("start_color", "FF0000"),
                    end_type="max",
                    end_color=formatting_config.get("end_color", "00FF00")
                )
                ws.conditional_formatting.add(data_range, rule)

    def add_chart(self, sheet_name: str, chart_config: Dict[str, Any]) -> None:
        """
        Add a chart to a worksheet

        Args:
            sheet_name: Name of the sheet to add chart to
            chart_config: Chart configuration
        """
        if not self.workbook or sheet_name not in self.workbook.sheetnames:
            logger.error(f"Sheet '{sheet_name}' not found")
            return

        ws = self.workbook[sheet_name]

        # Create bar chart (can be extended for other chart types)
        chart = BarChart()
        chart.title = chart_config.get("title", "Chart")
        chart.x_axis.title = chart_config.get("x_axis_title", "X Axis")
        chart.y_axis.title = chart_config.get("y_axis_title", "Y Axis")

        # Add data
        data_range = chart_config.get("data_range")
        categories_range = chart_config.get("categories_range")

        if data_range:
            data = Reference(ws, range_string=data_range)
            chart.add_data(data, titles_from_data=True)

        if categories_range:
            categories = Reference(ws, range_string=categories_range)
            chart.set_categories(categories)

        # Position chart
        chart_position = chart_config.get("position", "E2")
        ws.add_chart(chart, chart_position)

        logger.info(f"Added chart to sheet '{sheet_name}'")

    def save(self, filename: str) -> None:
        """
        Save the workbook to file

        Args:
            filename: Output filename
        """
        if not self.workbook:
            raise ValueError("No workbook created. Use create_workbook() first.")

        self.workbook.save(filename)
        logger.info(f"Saved Excel file: {filename}")

    def export_data(
        self,
        data: List[Dict[str, Any]],
        filename: str,
        sheet_name: str = "Data",
        include_summary: bool = True
    ) -> None:
        """
        Quick export of data to Excel with automatic formatting

        Args:
            data: List of dictionaries containing data
            filename: Output filename
            sheet_name: Name of the data sheet
            include_summary: Whether to include a summary sheet
        """
        if not data:
            logger.warning("No data to export")
            return

        self.create_workbook()

        # Determine columns from data
        all_keys = set()
        for row in data:
            all_keys.update(row.keys())

        columns = [ColumnConfig(name=key) for key in sorted(all_keys)]

        # Create main data sheet
        sheet_config = SheetConfig(
            name=sheet_name,
            columns=columns,
            data=data,
            freeze_panes="A2",
            auto_filter=True
        )

        self.add_sheet(sheet_config)

        # Create summary sheet if requested
        if include_summary:
            self._create_summary_sheet(data, columns)

        self.save(filename)

    def _create_summary_sheet(
        self, data: List[Dict[str, Any]], columns: List[ColumnConfig]
    ) -> None:
        """
        Create a summary sheet with statistics

        Args:
            data: Original data
            columns: Column configurations
        """
        summary_data = []

        # Basic statistics
        summary_data.append({
            "Metric": "Total Rows",
            "Value": len(data)
        })

        summary_data.append({
            "Metric": "Total Columns",
            "Value": len(columns)
        })

        # Column statistics
        for column in columns:
            values = [row.get(column.name) for row in data if row.get(column.name) is not None]

            if values:
                summary_data.append({
                    "Metric": f"{column.name} - Non-empty Count",
                    "Value": len(values)
                })

                # Try to calculate numeric statistics
                try:
                    numeric_values = [float(str(v).replace(',', '').replace('$', '').replace('%', '')) for v in values]
                    summary_data.extend([
                        {"Metric": f"{column.name} - Average", "Value": sum(numeric_values) / len(numeric_values)},
                        {"Metric": f"{column.name} - Min", "Value": min(numeric_values)},
                        {"Metric": f"{column.name} - Max", "Value": max(numeric_values)}
                    ])
                except (ValueError, TypeError):
                    pass

        # Create summary sheet
        summary_columns = [
            ColumnConfig(name="Metric", width=30),
            ColumnConfig(name="Value", width=15)
        ]

        summary_sheet_config = SheetConfig(
            name="Summary",
            columns=summary_columns,
            data=summary_data
        )

        self.add_sheet(summary_sheet_config)