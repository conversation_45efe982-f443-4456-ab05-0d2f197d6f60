"""
Data Extractor Module

This module provides comprehensive data extraction capabilities for various
file formats including PDFs, Word documents, images (OCR), and text files.
"""

import logging
import os
import tempfile
from pathlib import Path
from typing import Dict, List, Optional, Union, Any

import PyPDF2
import pdfplumber
from docx import Document
from PIL import Image
import pytesseract
import aiofiles
import asyncio
from dataclasses import dataclass

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class ExtractionResult:
    """Result of data extraction operation"""
    content: str
    metadata: Dict[str, Any]
    file_type: str
    success: bool
    error_message: Optional[str] = None


class DataExtractor:
    """
    Comprehensive data extraction system supporting multiple file formats
    """

    SUPPORTED_FORMATS = {
        'pdf': ['.pdf'],
        'docx': ['.docx', '.doc'],
        'image': ['.png', '.jpg', '.jpeg', '.tiff', '.bmp', '.gif'],
        'text': ['.txt', '.csv', '.json', '.xml', '.html']
    }

    def __init__(self, tesseract_config: Optional[str] = None):
        """
        Initialize the data extractor

        Args:
            tesseract_config: Custom tesseract configuration string
        """
        self.tesseract_config = tesseract_config or '--psm 6'
        self._validate_dependencies()

    def _validate_dependencies(self) -> None:
        """Validate that required dependencies are available"""
        try:
            # Test tesseract availability
            pytesseract.get_tesseract_version()
            logger.info("Tesseract OCR is available")
        except Exception as e:
            logger.warning(f"Tesseract OCR not available: {e}")

    def get_file_type(self, file_path: Union[str, Path]) -> str:
        """
        Determine file type based on extension

        Args:
            file_path: Path to the file

        Returns:
            File type category
        """
        file_path = Path(file_path)
        extension = file_path.suffix.lower()

        for file_type, extensions in self.SUPPORTED_FORMATS.items():
            if extension in extensions:
                return file_type

        return 'unknown'

    async def extract_from_file(
        self, file_path: Union[str, Path]
    ) -> ExtractionResult:
        """
        Extract data from a file

        Args:
            file_path: Path to the file to extract data from

        Returns:
            ExtractionResult containing extracted content and metadata
        """
        file_path = Path(file_path)

        if not file_path.exists():
            return ExtractionResult(
                content="",
                metadata={},
                file_type="unknown",
                success=False,
                error_message=f"File not found: {file_path}"
            )

        file_type = self.get_file_type(file_path)

        try:
            if file_type == 'pdf':
                return await self._extract_from_pdf(file_path)
            elif file_type == 'docx':
                return await self._extract_from_docx(file_path)
            elif file_type == 'image':
                return await self._extract_from_image(file_path)
            elif file_type == 'text':
                return await self._extract_from_text(file_path)
            else:
                return ExtractionResult(
                    content="",
                    metadata={},
                    file_type=file_type,
                    success=False,
                    error_message=f"Unsupported file type: {file_type}"
                )

        except Exception as e:
            logger.error(f"Error extracting from {file_path}: {e}")
            return ExtractionResult(
                content="",
                metadata={},
                file_type=file_type,
                success=False,
                error_message=str(e)
            )

    async def _extract_from_pdf(self, file_path: Path) -> ExtractionResult:
        """Extract text from PDF files using multiple methods"""
        content = ""
        metadata = {
            'pages': 0,
            'extraction_method': 'pdfplumber',
            'file_size': file_path.stat().st_size
        }

        try:
            # Try pdfplumber first (better for complex layouts)
            with pdfplumber.open(file_path) as pdf:
                metadata['pages'] = len(pdf.pages)
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        content += page_text + "\n"

            # If pdfplumber didn't extract much, try PyPDF2
            if len(content.strip()) < 100:
                metadata['extraction_method'] = 'pypdf2'
                content = ""

                with open(file_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    metadata['pages'] = len(pdf_reader.pages)

                    for page in pdf_reader.pages:
                        page_text = page.extract_text()
                        if page_text:
                            content += page_text + "\n"

            return ExtractionResult(
                content=content.strip(),
                metadata=metadata,
                file_type='pdf',
                success=True
            )

        except Exception as e:
            logger.error(f"Error extracting PDF {file_path}: {e}")
            return ExtractionResult(
                content="",
                metadata=metadata,
                file_type='pdf',
                success=False,
                error_message=str(e)
            )

    async def _extract_from_docx(self, file_path: Path) -> ExtractionResult:
        """Extract text from Word documents"""
        content = ""
        metadata = {
            'paragraphs': 0,
            'file_size': file_path.stat().st_size
        }

        try:
            doc = Document(file_path)
            paragraphs = []

            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    paragraphs.append(paragraph.text)

            content = "\n".join(paragraphs)
            metadata['paragraphs'] = len(paragraphs)

            return ExtractionResult(
                content=content,
                metadata=metadata,
                file_type='docx',
                success=True
            )

        except Exception as e:
            logger.error(f"Error extracting DOCX {file_path}: {e}")
            return ExtractionResult(
                content="",
                metadata=metadata,
                file_type='docx',
                success=False,
                error_message=str(e)
            )

    async def _extract_from_image(self, file_path: Path) -> ExtractionResult:
        """Extract text from images using OCR"""
        content = ""
        metadata = {
            'file_size': file_path.stat().st_size,
            'ocr_method': 'tesseract'
        }

        try:
            # Open and process image
            with Image.open(file_path) as img:
                metadata['image_size'] = img.size
                metadata['image_mode'] = img.mode

                # Convert to RGB if necessary
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                # Extract text using tesseract
                content = pytesseract.image_to_string(
                    img,
                    config=self.tesseract_config
                )

            return ExtractionResult(
                content=content.strip(),
                metadata=metadata,
                file_type='image',
                success=True
            )

        except Exception as e:
            logger.error(f"Error extracting from image {file_path}: {e}")
            return ExtractionResult(
                content="",
                metadata=metadata,
                file_type='image',
                success=False,
                error_message=str(e)
            )

    async def _extract_from_text(self, file_path: Path) -> ExtractionResult:
        """Extract content from text-based files"""
        content = ""
        metadata = {
            'file_size': file_path.stat().st_size,
            'encoding': 'utf-8'
        }

        try:
            # Try different encodings
            encodings = ['utf-8', 'latin-1', 'cp1252']

            for encoding in encodings:
                try:
                    async with aiofiles.open(
                        file_path, 'r', encoding=encoding
                    ) as file:
                        content = await file.read()
                    metadata['encoding'] = encoding
                    break
                except UnicodeDecodeError:
                    continue

            if not content:
                raise ValueError(
                    "Could not decode file with any supported encoding"
                )

            return ExtractionResult(
                content=content,
                metadata=metadata,
                file_type='text',
                success=True
            )

        except Exception as e:
            logger.error(f"Error extracting text file {file_path}: {e}")
            return ExtractionResult(
                content="",
                metadata=metadata,
                file_type='text',
                success=False,
                error_message=str(e)
            )

    async def extract_from_bytes(
        self, file_bytes: bytes, filename: str
    ) -> ExtractionResult:
        """
        Extract data from bytes (useful for uploaded files)

        Args:
            file_bytes: File content as bytes
            filename: Original filename to determine file type

        Returns:
            ExtractionResult containing extracted content and metadata
        """
        # Create temporary file
        with tempfile.NamedTemporaryFile(
            delete=False, suffix=Path(filename).suffix
        ) as tmp_file:
            tmp_file.write(file_bytes)
            tmp_path = tmp_file.name

        try:
            # Extract from temporary file
            result = await self.extract_from_file(tmp_path)
            # Update metadata to include original filename
            result.metadata['original_filename'] = filename
            return result
        finally:
            # Clean up temporary file
            try:
                os.unlink(tmp_path)
            except OSError:
                pass

    async def batch_extract(
        self, file_paths: List[Union[str, Path]]
    ) -> List[ExtractionResult]:
        """
        Extract data from multiple files concurrently

        Args:
            file_paths: List of file paths to process

        Returns:
            List of ExtractionResult objects
        """
        tasks = [self.extract_from_file(path) for path in file_paths]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Handle any exceptions that occurred
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append(ExtractionResult(
                    content="",
                    metadata={},
                    file_type="unknown",
                    success=False,
                    error_message=f"Exception during extraction: {str(result)}"
                ))
            else:
                processed_results.append(result)

        return processed_results