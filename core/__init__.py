"""
DataEntryAI Core Module

This module provides the core functionality for the DataEntryAI application,
including data extraction, web crawling, AI-powered data matching,
Excel export, and configuration management.
"""

from .config import ConfigManager
from .extractor import DataExtractor, ExtractionResult
from .crawler import WebCrawler, CrawlResult, CrawlConfig
from .matcher import DataMatcher, MatchResult, DataSchema, FieldSchema, DataType
from .excel_writer import ExcelWriter, SheetConfig, ColumnConfig, CellStyle

__version__ = "1.0.0"
__author__ = "DataEntryAI Team"

__all__ = [
    "ConfigManager",
    "DataExtractor",
    "ExtractionResult",
    "WebCrawler",
    "CrawlResult",
    "CrawlConfig",
    "DataMatcher",
    "MatchResult",
    "DataSchema",
    "FieldSchema",
    "DataType",
    "ExcelWriter",
    "SheetConfig",
    "ColumnConfig",
    "CellStyle"
]